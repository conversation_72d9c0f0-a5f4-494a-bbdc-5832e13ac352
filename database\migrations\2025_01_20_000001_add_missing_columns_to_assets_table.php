<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToAssetsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('assets', function (Blueprint $table) {
            // Add all the missing columns that are expected by the Assets model
            $table->string('Code')->nullable();
            $table->string('Asset_Type')->nullable();
            $table->string('Depreciation_Method')->nullable();
            $table->string('Asset_Type_En')->nullable();
            $table->string('Depreciation_Method_En')->nullable();
            $table->date('Purchases_Date')->nullable();
            $table->date('Operation_Date')->nullable();
            $table->decimal('Cost', 15, 2)->nullable();
            $table->decimal('Previous_Depreciation', 15, 2)->nullable();
            $table->decimal('Asset_Net', 15, 2)->nullable();
            $table->decimal('Annual_Depreciation_Ratio', 8, 2)->nullable();
            $table->decimal('Annual_Depreciation', 15, 2)->nullable();
            $table->integer('Life_Span')->nullable();
            $table->text('Image')->nullable();
            $table->text('Note')->nullable();
            $table->string('Depreciation_Expenses')->nullable();
            $table->string('Depreciation_Complex')->nullable();
            $table->string('Main_Account')->nullable();
            $table->string('Account')->nullable();
            $table->string('User')->nullable();
            $table->decimal('Draw', 15, 4)->nullable();
            $table->string('Coin')->nullable();
            $table->string('Cost_Center')->nullable();
            $table->string('Branch')->nullable();
            $table->integer('Sort_Asset')->nullable();
            $table->string('Vendor')->nullable();
            $table->string('Safe')->nullable();
            $table->string('Ehlak')->nullable();
            $table->string('Payment_Method')->nullable();
            $table->decimal('M1', 15, 2)->nullable();
            $table->decimal('M2', 15, 2)->nullable();
            $table->decimal('M3', 15, 2)->nullable();
            $table->decimal('M4', 15, 2)->nullable();
            $table->decimal('M5', 15, 2)->nullable();
            $table->decimal('M6', 15, 2)->nullable();
            $table->decimal('M7', 15, 2)->nullable();
            $table->decimal('M8', 15, 2)->nullable();
            $table->decimal('M9', 15, 2)->nullable();
            $table->decimal('M10', 15, 2)->nullable();
            $table->decimal('M11', 15, 2)->nullable();
            $table->decimal('M12', 15, 2)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('assets', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn([
                'Code', 'Asset_Type', 'Depreciation_Method', 'Asset_Type_En', 'Depreciation_Method_En',
                'Purchases_Date', 'Operation_Date', 'Cost', 'Previous_Depreciation', 'Asset_Net',
                'Annual_Depreciation_Ratio', 'Annual_Depreciation', 'Life_Span', 'Image', 'Note',
                'Depreciation_Expenses', 'Depreciation_Complex', 'Main_Account', 'Account', 'User',
                'Draw', 'Coin', 'Cost_Center', 'Branch', 'Sort_Asset', 'Vendor', 'Safe', 'Ehlak',
                'Payment_Method', 'M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7', 'M8', 'M9', 'M10', 'M11', 'M12'
            ]);
        });
    }
}

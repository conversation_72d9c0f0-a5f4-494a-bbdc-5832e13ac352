<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToProductsStartPeriodsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('products_start_periods', function (Blueprint $table) {
            // Add all the missing columns that are expected by the ProductsStartPeriods model
            $table->string('P_Code')->nullable();
            $table->decimal('Qty', 15, 4)->nullable();
            $table->decimal('SmallQty', 15, 4)->nullable();
            $table->string('SmallCode')->nullable();
            $table->decimal('Price', 15, 2)->nullable();
            $table->decimal('Total', 15, 2)->nullable();
            $table->date('Date')->nullable();
            $table->string('P_Ar_Name')->nullable();
            $table->string('P_En_Name')->nullable();
            $table->string('V_Name')->nullable();
            $table->string('VV_Name')->nullable();
            $table->string('User')->nullable(); // Foreign key to admins
            $table->decimal('Old_Qty', 15, 4)->nullable();
            $table->date('Exp_Date')->nullable();
            $table->string('SP_ID')->nullable(); // Foreign key to start_periods
            $table->string('Unit')->nullable(); // Foreign key to measuerments
            $table->string('Product')->nullable(); // Foreign key to products
            $table->string('Store')->nullable(); // Foreign key to stores
            $table->string('V1')->nullable(); // Foreign key to sub_virables
            $table->string('V2')->nullable(); // Foreign key to sub_virables
            $table->string('Patch_Number')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('products_start_periods', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn([
                'P_Code', 'Qty', 'SmallQty', 'SmallCode', 'Price', 'Total', 'Date', 'P_Ar_Name', 'P_En_Name',
                'V_Name', 'VV_Name', 'User', 'Old_Qty', 'Exp_Date', 'SP_ID', 'Unit', 'Product', 'Store',
                'V1', 'V2', 'Patch_Number'
            ]);
        });
    }
}

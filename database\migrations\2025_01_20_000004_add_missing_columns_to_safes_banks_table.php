<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToSafesBanksTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('safes_banks', function (Blueprint $table) {
            // Add all the missing columns that are expected by the SafesBanks model
            $table->string('Code')->nullable();
            $table->date('Date')->nullable();
            $table->integer('Type')->nullable(); // 1 = Safe, 2 = Bank
            $table->text('Note')->nullable();
            $table->string('Account')->nullable(); // Foreign key to acccounting_manuals
            $table->string('Branch')->nullable(); // Foreign key to branches
            $table->string('User')->nullable(); // Foreign key to admins
            $table->decimal('Service_Fee', 15, 2)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('safes_banks', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn([
                'Code', 'Date', 'Type', 'Note', 'Account', 'Branch', 'User', 'Service_Fee'
            ]);
        });
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToFifoQtiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('fifo_qties', function (Blueprint $table) {
            // Add all the missing columns that are expected by the FifoQty model
            $table->decimal('Qty', 15, 4)->nullable();
            $table->decimal('Original_Qty', 15, 4)->nullable();
            $table->string('P_Ar_Name')->nullable();
            $table->string('P_En_Name')->nullable();
            $table->string('P_Code')->nullable();
            $table->string('PP_Code')->nullable();
            $table->string('PPP_Code')->nullable();
            $table->string('PPPP_Code')->nullable();
            $table->string('V_Name')->nullable();
            $table->string('VV_Name')->nullable();
            $table->decimal('Cost_Price', 15, 2)->nullable();
            $table->string('Store')->nullable(); // Foreign key to stores
            $table->string('Unit')->nullable(); // Foreign key to measuerments
            $table->string('Product')->nullable(); // Foreign key to products
            $table->string('V1')->nullable(); // Foreign key to sub_virables
            $table->string('V2')->nullable(); // Foreign key to sub_virables
            $table->string('Low_Unit')->nullable(); // Foreign key to measuerments
            $table->date('Exp_Date')->nullable();
            $table->string('SearchCode1')->nullable();
            $table->string('SearchCode2')->nullable();
            $table->string('Group')->nullable(); // Foreign key to items_groups
            $table->string('Brand')->nullable(); // Foreign key to brands
            $table->string('Branch')->nullable(); // Foreign key to branches
            $table->date('Purchases_Date')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('fifo_qties', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn([
                'Qty', 'Original_Qty', 'P_Ar_Name', 'P_En_Name', 'P_Code', 'PP_Code', 'PPP_Code', 'PPPP_Code',
                'V_Name', 'VV_Name', 'Cost_Price', 'Store', 'Unit', 'Product', 'V1', 'V2', 'Low_Unit',
                'Exp_Date', 'SearchCode1', 'SearchCode2', 'Group', 'Brand', 'Branch', 'Purchases_Date'
            ]);
        });
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToProductsQtiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('products_qties', function (Blueprint $table) {
            // Add all the missing columns that are expected by the ProductsQty model
            $table->decimal('Qty', 15, 4)->nullable();
            $table->string('P_Ar_Name')->nullable();
            $table->string('P_En_Name')->nullable();
            $table->string('P_Code')->nullable();
            $table->string('PP_Code')->nullable();
            $table->string('PPP_Code')->nullable();
            $table->string('PPPP_Code')->nullable();
            $table->string('V_Name')->nullable();
            $table->string('VV_Name')->nullable();
            $table->decimal('Price', 15, 2)->nullable();
            $table->decimal('TotalCost', 15, 2)->nullable();
            $table->string('Pro_Stores')->nullable(); // Foreign key to products_stores
            $table->string('Store')->nullable(); // Foreign key to stores
            $table->string('Unit')->nullable(); // Foreign key to measuerments
            $table->string('Product')->nullable(); // Foreign key to products
            $table->decimal('Original', 15, 4)->nullable();
            $table->string('V1')->nullable(); // Foreign key to sub_virables
            $table->string('V2')->nullable(); // Foreign key to sub_virables
            $table->string('Low_Unit')->nullable(); // Foreign key to measuerments
            $table->string('Patch_Number')->nullable();
            $table->date('Exp_Date')->nullable();
            $table->decimal('Price_Sale', 15, 2)->nullable();
            $table->string('SearchCode1')->nullable();
            $table->string('SearchCode2')->nullable();
            $table->string('Group')->nullable(); // Foreign key to items_groups
            $table->string('Brand')->nullable(); // Foreign key to brands
            $table->string('Branch')->nullable(); // Foreign key to branches
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('products_qties', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn([
                'Qty', 'P_Ar_Name', 'P_En_Name', 'P_Code', 'PP_Code', 'PPP_Code', 'PPPP_Code',
                'V_Name', 'VV_Name', 'Price', 'TotalCost', 'Pro_Stores', 'Store', 'Unit', 'Product',
                'Original', 'V1', 'V2', 'Low_Unit', 'Patch_Number', 'Exp_Date', 'Price_Sale',
                'SearchCode1', 'SearchCode2', 'Group', 'Brand', 'Branch'
            ]);
        });
    }
}

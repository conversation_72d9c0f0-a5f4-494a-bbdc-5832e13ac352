<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToProductUnitsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('product_units', function (Blueprint $table) {
            // Add all the missing columns that are expected by the ProductUnits model
            $table->decimal('Rate', 15, 4)->nullable();
            $table->string('Barcode')->nullable();
            $table->decimal('Price', 15, 2)->nullable();
            $table->decimal('Price_Two', 15, 2)->nullable();
            $table->decimal('Price_Three', 15, 2)->nullable();
            $table->string('P_Ar_Name')->nullable();
            $table->string('P_En_Name')->nullable();
            $table->string('P_Type')->nullable();
            $table->string('Unit')->nullable(); // Foreign key to measuerments
            $table->string('Product')->nullable(); // Foreign key to products
            $table->integer('Def')->default(0); // Default unit flag
            $table->string('Brand')->nullable(); // Foreign key to brands
            $table->string('Group')->nullable(); // Foreign key to items_groups
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('product_units', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn([
                'Rate', 'Barcode', 'Price', 'Price_Two', 'Price_Three', 'P_Ar_Name', 'P_En_Name', 
                'P_Type', 'Unit', 'Product', 'Def', 'Brand', 'Group'
            ]);
        });
    }
}

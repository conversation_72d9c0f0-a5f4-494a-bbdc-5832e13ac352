<?php $__env->startSection('content'); ?>
<?php
use App\Models\CompanyData;
$Def=CompanyData::orderBy('id','desc')->first();
?>



  <title><?php echo e(trans('admin.Cost_Centers_Report')); ?></title>

<style>
    .over-flow{
        overflow:auto;
    }
    .table-width{
        width:170%;
    }
    @media  print{
            .over-flow{
            overflow:visible;
        }
        .table-width{
            width:100%;
        }  
        th{font-size:16px;}
        .panel-content{
            padding:0;
        }
        .form-group {
        margin-bottom:0;
        }
        @page{
            /*size:landscape;*/
            margin:0;
            padding:0;
        }
    }
</style>

   <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb no-print">
                        <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Accounts_Reports')); ?> </a></li>
                        <li class="breadcrumb-item active"><?php echo e(trans('admin.Cost_Centers_Report')); ?></li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                class="js-get-date"></span></li>
                    </ol>
                   
                   
                   
                   
                            <!-- Head Of Print -->
                     <div class="row Headerrr">
                      <div class="col-4">
                            <div id="panel-1" class="panel d-none d-print-block" style="margin-top:30px;">
                      <div class="row invoive-info ">
                       
                                          <div  style="text-align: center;" class="col-md-4 col-sm-4 col-4 invoice-client-info">
                                                <div class="style-info">
                                                    <h1 class="HeaderInvoice" style="text-align: center; font-size:15px;" class="m-0">
                                               <?php if(!empty($Def->Name)): ?>
                      <?php echo e($Def->Name); ?>

                      <?php else: ?>
                       <?php echo e(trans('admin.Ost')); ?>

                      <?php endif; ?>         
                                                    </h1>
                              <h3  style="text-align: center;font-size:15px" class=" m-10">
                         <?php if(!empty($Def->Print_Text)): ?>
                      <?php echo e($Def->Print_Text); ?>

                      <?php endif; ?>                
                                                    </h3>
                                                </div>
                                            </div>
                                        
                                        </div>
                                        <hr>
                                        </div>
                                        </div>  
                         
                              <div class="col-4 text-center" >
                            <div id="panel-1" class="panel d-none d-print-block" style="margin-top:30px;">
                      <div class="row invoive-info " style="display:flex;  justify-content: center;">
                                          
                                            <div style="text-align: center;" class="col-md-4 col-sm-4 col-4 invoice-client-info" style="border:unset;">
                                                <h1 style="text-align: center;" class="HeaderInvoice"> <?php echo e(trans('admin.Cost_Centers_Report')); ?> </h1>
                                            </div>
                                 
                                        </div>
                                        <hr>
                                        </div>
                                        </div>
                                        
                                <div class="col-4">
                            <div id="panel-1" class="panel d-none d-print-block" style="margin-top:30px;">
                      <div class="row invoive-info "style="display:flex;         justify-content: flex-end;">
                                         
                                   <div  style="text-align: center; " class="col-md-4 col-sm-4 col-4 invoice-client-info">
                         <div class="style-info">
                       <?php if(!empty($Def->Logo)): ?>         
    <img class="logoPri" style="height: 50px; " class="img-fluid" src="<?php echo e(URL::to($Def->Logo)); ?>" alt="Logo" />
                                       
                    <?php else: ?>  
 <img class="logoPri" style="height: 50px; width: 150px;" class="img-fluid" src="https://klarerp.com/site/img/theme/logo.png" alt="Logo" />
                      <?php endif; ?>
               
                                       </div>
                                            </div>
                         
                    
                                        </div>
                                        <hr>
                                        </div>
                                        </div>            
                                        
                                        </div>
        
                    <!-- data entry -->
                    <div class="row">
                        <div class="col-lg-12">
                            <div id="panel-2" class="panel">
                                <div class="panel-hdr  no-print">
                                    <h2>
                                        <span class="fw-300"><i>   <?php echo e(trans('admin.Filter')); ?></i></span>
                                    </h2>
                                </div>
                                <div class="panel-container show">
                                    <div class="panel-content">
                                        <form class="form-row">
                                            <div class="form-group col-md-3 col-4">
                                                <label class="form-label" for="simpleinput"><?php echo e(trans('admin.From')); ?></label>
                                                <input type="date" id="from" value="<?php echo e(date('Y-m-d')); ?>" class="form-control">
                                            </div>
                                            <div class="form-group col-md-3 col-4">
                                                <label class="form-label" for="simpleinput"><?php echo e(trans('admin.To')); ?></label>
                                                <input type="date" id="to" value="<?php echo e(date('Y-m-d')); ?>" class="form-control">
                                            </div>
                                           
                                            <div class="form-group col-md-3  no-print">
                                                <label class="form-label" for="">  <?php echo e(trans('admin.Coin')); ?></label>
                            
              <select id="coin"  data-placeholder="<?php echo e(trans('admin.Coin')); ?>" class="js-data-example-ajax form-control">
                                <option value="" selected><?php echo e(trans('admin.Coin')); ?></option>                
                                                </select>
                                                
                                            </div>
                                            <div class="form-group col-md-3 col-4">
                                                <label class="form-label" for="">  <?php echo e(trans('admin.Account')); ?></label>
                                          
                <select id="account" data-placeholder="<?php echo e(trans('admin.Account')); ?>" class="js-data-example-ajax form-control">
                                 <option value="" selected><?php echo e(trans('admin.Account')); ?></option>                    
                                                </select>
                                            </div>
                                            <div class="form-group col-md-3  no-print">
                                                <label class="form-label" for="">  <?php echo e(trans('admin.Bond_Type')); ?> </label>
                                                <select class="select2 form-control w-100" id="type">
                                                        <option value=""> <?php echo e(trans('admin.Bond_Type')); ?></option>
                        <option value="القيود اليومية"> <?php echo e(trans('admin.Journalizing')); ?></option>
                   <option value="سند قبض"> <?php echo e(trans('admin.Receipt_Voucher')); ?></option>
                   <option value="سند صرف"> <?php echo e(trans('admin.Payment_Voucher')); ?></option>
                   <option value="القيد الإفتتاحي"> <?php echo e(trans('admin.Opening_Entries')); ?></option>
                   <option value="الشيكات الصادرة"> <?php echo e(trans('admin.Exporting_Checks')); ?></option>
                   <option value="الشيكات الواردة"> <?php echo e(trans('admin.Incoming_checks')); ?></option>
                 <option value="اصناف بداية فترة"> <?php echo e(trans('admin.Start_Period_Products')); ?></option>
                   <option value="تسوية بالعجز"> <?php echo e(trans('admin.Dificit_Settlement')); ?></option>
                   <option value="تسوية بالزيادة"> <?php echo e(trans('admin.Execess_Settlement')); ?></option>
                   <option value="تحويلات الخزائن"> <?php echo e(trans('admin.Safes_Transfer')); ?></option>
                   <option value="تحويلات المخازن"> <?php echo e(trans('admin.Stores_Transfers')); ?></option>
                   <option value="المشتريات"> <?php echo e(trans('admin.Purchases')); ?></option>
                   <option value="مرتجع المشتريات"> <?php echo e(trans('admin.Return_Purchases')); ?></option>
                   <option value="المبيعات"> <?php echo e(trans('admin.Sales')); ?></option>
                   <option value="مرتجع مبيعات"> <?php echo e(trans('admin.Return_Sales')); ?></option>
                   <option value="سلفة موظف"> <?php echo e(trans('admin.Emp_Borrow')); ?></option>
                   <option value="قرض موظف"> <?php echo e(trans('admin.Emp_Loan')); ?></option>
                   <option value="صرف راتب"> <?php echo e(trans('admin.AddSalary')); ?></option>
                   <option value="وصل أمانة"> <?php echo e(trans('admin.Insurance_Paper')); ?></option>
                   <option value="استلام وصل أمانة"> <?php echo e(trans('admin.Insurance_Paper_Recived')); ?></option>
                   <option value="مصاريف الأصول"> <?php echo e(trans('admin.AssetExpenses')); ?></option>
                   <option value="صرف بضاعة"> <?php echo e(trans('admin.Exchange_Goods')); ?></option>
                   <option value="استلام بضاعة"> <?php echo e(trans('admin.Recived_Goods')); ?></option>
                   <option value="صرف ارباح"> <?php echo e(trans('admin.Spend_Profits')); ?></option>
                   <option value="بيع اصل"> <?php echo e(trans('admin.Asset_Sale')); ?></option>
                   <option value="شراء أصل"> <?php echo e(trans('admin.Purchases_Asset')); ?></option>
                   <option value="الصيانة"> <?php echo e(trans('admin.Maintenance')); ?></option>
                   <option value="مرتجع فاتورة الصيانة"> <?php echo e(trans('admin.ReturnMaintainceBill')); ?></option>
                   <option value="صرف عمولات"> <?php echo e(trans('admin.ExchangeCommissions')); ?></option>
                   <option value="التصنيع"> <?php echo e(trans('admin.Manufacturing')); ?></option>
                   <option value="اشتراكات"> <?php echo e(trans('admin.SalesSubscribes')); ?></option>
                   <option value="الهالك"> <?php echo e(trans('admin.Depreciation')); ?></option>
                   <option value="البوليصه"> <?php echo e(trans('admin.')); ?></option>
                   <option value="مبيعات وقود"> <?php echo e(trans('admin.SalesPetrol')); ?></option>
                   <option value="دفع شيك وارد"> <?php echo e(trans('admin.PayIncomChecks')); ?></option>
                   <option value="رفض الشيكات الواردة"> <?php echo e(trans('admin.RefuseIncomChecks')); ?></option>
                   <option value="رفض الشيكات الصادرة"> <?php echo e(trans('admin.RefuseExportChecks')); ?></option>
                   <option value="دفع شيك صادر"> <?php echo e(trans('admin.PayExportChecks')); ?></option> 
                                                </select>
                                            </div>

                                            <div class="form-group col-md-3  no-print">
                                                <label class="form-label" for="">   <?php echo e(trans('admin.Cost_Center')); ?><span style="color: red">*</span></label>
                          <select id="cost" data-placeholder="<?php echo e(trans('admin.Cost_Center')); ?>" class="js-data-example-ajax form-control">    <option value="" selected><?php echo e(trans('admin.Cost_Center')); ?></option>  </select>
                                            </div>
                                            <div class="form-group col-md-3  no-print">
                                                <label class="form-label" for="">  <?php echo e(trans('admin.User')); ?></label>
                             <select id="user" data-placeholder="<?php echo e(trans('admin.User')); ?>" class="js-data-example-ajax form-control">    <option value="" selected><?php echo e(trans('admin.User')); ?></option>  </select>
                                            </div>

                                            <div class="form-group col-md-3  no-print">
                                                <div class="buttons mt-4">
                                                    <a class="btn btn-primary w-100" href="#" onclick="window.print()"> <i class="fal fa-print"></i> <?php echo e(trans('admin.Print')); ?></a>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
       

                    <div class="row hide-table">
                        <div class="col-xl-12">
                            <div id="panel-1" class="panel">
                                <div class="panel-hdr no-print">
                                    <h2>
                                       <?php echo e(trans('admin.Cost_Centers_Report')); ?> 
                                    </h2>

                                    <div class="panel-toolbar">
                                        <button class="btn btn-primary btn-sm" data-toggle="dropdown">Table
                                            Style</button>
                                        <div
                                            class="dropdown-menu dropdown-menu-animated dropdown-menu-right position-absolute pos-top">
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-bordered" data-target="#dt-basic-example"> Bordered
                                                Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-sm"
                                                data-target="#dt-basic-example"> Smaller Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-dark"
                                                data-target="#dt-basic-example"> Table Dark </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-hover" data-target="#dt-basic-example"> Table Hover
                                            </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-stripe" data-target="#dt-basic-example"> Table
                                                Stripped </button>
                                            <div class="dropdown-divider m-0"></div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    tbody color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-tbody-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    thead color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-thead-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="panel-container show">
                                    <div class="panel-content">
                                        <!-- datatable start -->
                                        <div class="over-flow">
                                        <table id="X"
                                            class="table table-bordered table-hover table-striped">
                                            <thead class="bg-highlight">
                                                <tr>
                                                    <th> <?php echo e(trans('admin.Date')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Code')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Bond_Code')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Bond_Type')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Debitor')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Creditor')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Statement')); ?> </th>
                                                    <th><?php echo e(trans('admin.Account_Code')); ?> </th>
                                                    <th><?php echo e(trans('admin.Account_Name')); ?> </th>
                                                    <th><?php echo e(trans('admin.Cost_Centers')); ?></th>
                                                    <th><?php echo e(trans('admin.Coin')); ?></th>
                                                    <th><?php echo e(trans('admin.Draw')); ?></th>
                                                    <th><?php echo e(trans('admin.Coin_Debitor')); ?></th>
                                                    <th><?php echo e(trans('admin.Coin_Creditor')); ?></th>
                                                    <th><?php echo e(trans('admin.User')); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody class="Data">
                                           
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <th> <?php echo e(trans('admin.Date')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Code')); ?> </th>
                                                     <th> <?php echo e(trans('admin.Bond_Code')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Bond_Type')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Debitor')); ?> </th>
                                                    <th>  <?php echo e(trans('admin.Creditor')); ?> </th>
                                                    <th>  <?php echo e(trans('admin.Statement')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Account_Code')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Account_Name')); ?> </th>
                                                    <th><?php echo e(trans('admin.Cost_Centers')); ?></th>
                                                    <th><?php echo e(trans('admin.Coin')); ?></th>
                                                    <th><?php echo e(trans('admin.Draw')); ?></th>
                                                    <th><?php echo e(trans('admin.Coin_Debitor')); ?></th>
                                                    <th><?php echo e(trans('admin.Coin_Creditor')); ?></th>
                                                    <th class="no-print"><?php echo e(trans('admin.User')); ?></th>
                                                </tr>
                                            </tfoot>
                        
                                        </table>
                                        </div>
                                        <div id="mobile-overflow">
                                        <table class="table table-bordered table-hover table-striped w-100 mt-4 mobile-width th-td-width">
                                            <tbody>
                                                <tr>
                                                    <td><?php echo e(trans('admin.Numbers')); ?></td>
                                                    <td class="NUM"></td>
                                                    <td><?php echo e(trans('admin.Total_Debitor')); ?></td>
                                                    <td class="TD"></td>
                                                    <td><?php echo e(trans('admin.Total_Creditor')); ?></td>
                                                    <td class="TC"></td>
                                                    <td><?php echo e(trans('admin.Difference')); ?></td>
                                                    <td class="DIF"></td>
        
                                                </tr>
                                            </tbody>
                                        </table>
                                        </div>
                                        <!-- datatable end -->
                                    </div>
                       
                                </div>
                            </div>
                        </div>
                    </div>
                </main>

  

<?php $__env->stopSection(); ?>


<?php $__env->startPush('js'); ?>
   <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/summernote/summernote.js')); ?>"></script>
  <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
 
<!-- Search Selecet -->
 <script>
        $(document).ready(function()
        {
            $(function()
            {
                $('.select2').select2();

                $(".select2-placeholder-multiple").select2(
                {
                    placeholder: "Select State"
                });
                $(".js-hide-search").select2(
                {
                    minimumResultsForSearch: 1 / 0
                });
                $(".js-max-length").select2(
                {
                    maximumSelectionLength: 2,
                    placeholder: "Select maximum 2 items"
                });
                $(".select2-placeholder").select2(
                {
                    placeholder: "Select a state",
                    allowClear: true
                });

                $(".js-select2-icons").select2(
                {
                    minimumResultsForSearch: 1 / 0,
                    templateResult: icon,
                    templateSelection: icon,
                    escapeMarkup: function(elm)
                    {
                        return elm
                    }
                });

                function icon(elm)
                {
                    elm.element;
                    return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
                }

  
  $('#coin').select2({
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllCoins',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };            
        },
        data: function (params) {  
   
            
                   var query = {
                            search: params.term,
                        };
                                
            
              $.ajax({
                              url: 'AllCoinsJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('#coin').empty();  
                                  $.each(data, function(key, value){
   
                         $('#coin').append('<option value="'+ key +'">' + value + '</option>');
                   
                                  });
                                  
                                      fetch_customer_data();
   
    function fetch_customer_data(from = '',to = '',coin = '',account = '',type = '',cost = '',user = '',)
    {
     $.ajax({
      url:'Cost_Centers_ReportFilter',
      method:'GET',
      data:{from:from,to:to,coin:coin,account:account,type:type,cost:cost,user:user},
      dataType:'json',
      success:function(data)
      {
          

       $('.Data').html(data.table_data);
       $('.TD').html(data.totalD);
       $('.TC').html(data.totalC);
       $('.NUM').html(data.totalN);
       $('.DIF').html(data.dif);
          

          
      }
     })
    }

     var from = $('#from').val();     
     var to = $('#to').val();     
     var coin = $('#coin').val();     
     var account = $('#account').val();     
     var type = $('#type').val();     
     var cost = $('#cost').val();     
     var user = $('#user').val();     
     fetch_customer_data(from,to,coin,account,type,cost,user);

                                  
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
            
            
            
        }
        }
  });

           
    
$('#coin').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});
            
        
  $('#account').select2({
    placeholder: "select...",
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllSubAccounts',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };
            
            	console.log(data);
            
        },
          data: function (params) {  
   
            
                   var query = {
                            search: params.term,
                        };
                                
            
              $.ajax({
                              url: 'AllSubAccountsJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('#account').empty();  
                                  $.each(data, function(key, value){
   
                         $('#account').append('<option value="'+ key +'">' + value + '</option>');
                   
                                  });
                                                                       fetch_customer_data();
   
    function fetch_customer_data(from = '',to = '',coin = '',account = '',type = '',cost = '',user = '',)
    {
     $.ajax({
      url:'Cost_Centers_ReportFilter',
      method:'GET',
      data:{from:from,to:to,coin:coin,account:account,type:type,cost:cost,user:user},
      dataType:'json',
      success:function(data)
      {
          

       $('.Data').html(data.table_data);
       $('.TD').html(data.totalD);
       $('.TC').html(data.totalC);
       $('.NUM').html(data.totalN);
       $('.DIF').html(data.dif);
          

          
      }
     })
    }

     var from = $('#from').val();     
     var to = $('#to').val();     
     var coin = $('#coin').val();     
     var account = $('#account').val();     
     var type = $('#type').val();     
     var cost = $('#cost').val();     
     var user = $('#user').val();     
     fetch_customer_data(from,to,coin,account,type,cost,user);

                                  
                                  
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
            
            
            
        }
    }
  });

    
$('#account').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});
           
                
      $('#cost').select2({
    placeholder: "select...",
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllCostss',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };
            
            	console.log(data);
            
        },
    
         data: function (params) {  
   
            
                   var query = {
                            search: params.term,
                        };
                                
            
              $.ajax({
                              url: 'AllCostssJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('#cost').empty();  
                                  $.each(data, function(key, value){
   
                         $('#cost').append('<option value="'+ key +'">' + value + '</option>');
                   
                                  });
                                  
                                                                 fetch_customer_data();
   
    function fetch_customer_data(from = '',to = '',coin = '',account = '',type = '',cost = '',user = '',)
    {
     $.ajax({
      url:'Cost_Centers_ReportFilter',
      method:'GET',
      data:{from:from,to:to,coin:coin,account:account,type:type,cost:cost,user:user},
      dataType:'json',
      success:function(data)
      {
          

       $('.Data').html(data.table_data);
       $('.TD').html(data.totalD);
       $('.TC').html(data.totalC);
       $('.NUM').html(data.totalN);
       $('.DIF').html(data.dif);
          

          
      }
     })
    }

     var from = $('#from').val();     
     var to = $('#to').val();     
     var coin = $('#coin').val();     
     var account = $('#account').val();     
     var type = $('#type').val();     
     var cost = $('#cost').val();     
     var user = $('#user').val();     
     fetch_customer_data(from,to,coin,account,type,cost,user);
      
                                  
                                  
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
            
            
            
        }
        
    }
  });

    
$('#cost').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});
      
                
                
  $('#user').select2({
    placeholder: "select...",
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllUsers',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };
            
            	console.log(data);
            
        },
         data: function (params) {  
   
            
                   var query = {
                            search: params.term,
                        };
                                
            
              $.ajax({
                              url: 'AllUsersJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('#user').empty();  
                                  $.each(data, function(key, value){
   
                         $('#user').append('<option value="'+ key +'">' + value + '</option>');
                   
                                  });
                                  
                                                                     fetch_customer_data();
   
    function fetch_customer_data(from = '',to = '',coin = '',account = '',type = '',cost = '',user = '',)
    {
     $.ajax({
      url:'Cost_Centers_ReportFilter',
      method:'GET',
      data:{from:from,to:to,coin:coin,account:account,type:type,cost:cost,user:user},
      dataType:'json',
      success:function(data)
      {
          

       $('.Data').html(data.table_data);
       $('.TD').html(data.totalD);
       $('.TC').html(data.totalC);
       $('.NUM').html(data.totalN);
       $('.DIF').html(data.dif);
          

          
      }
     })
    }

     var from = $('#from').val();     
     var to = $('#to').val();     
     var coin = $('#coin').val();     
     var account = $('#account').val();     
     var type = $('#type').val();     
     var cost = $('#cost').val();     
     var user = $('#user').val();     
     fetch_customer_data(from,to,coin,account,type,cost,user);
  
                                  
                                  
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
            
            
            
        }
    }
  });

    
$('#user').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});
                      

             
            });
        });



    </script>

<script>
    var autoSave = $('#autoSave');
    var interval;
    var timer = function()
    {
        interval = setInterval(function()
        {
            //start slide...
            if (autoSave.prop('checked'))
                saveToLocal();

            clearInterval(interval);
        }, 3000);
    };

    //save
    var saveToLocal = function()
    {
        localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
        console.log("saved");
    }

    //delete 
    var removeFromLocal = function()
    {
        localStorage.removeItem("summernoteData");
        $('#saveToLocal').summernote('reset');
    }

    $(document).ready(function()
    {
        //init default
        $('.js-summernote').summernote(
        {
            height: 200,
            tabsize: 2,
            placeholder: "Type here...",
            dialogsFade: true,
            toolbar: [
                ['style', ['style']],
                ['font', ['strikethrough', 'superscript', 'subscript']],
                ['font', ['bold', 'italic', 'underline', 'clear']],
                ['fontsize', ['fontsize']],
                ['fontname', ['fontname']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['height', ['height']]
                ['table', ['table']],
                ['insert', ['link', 'picture', 'video']],
                ['view', ['fullscreen', 'codeview', 'help']]
            ],
            callbacks:
            {
                //restore from localStorage
                onInit: function(e)
                {
                    $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
                },
                onChange: function(contents, $editable)
                {
                    clearInterval(interval);
                    timer();
                }
            }
        });

        //load emojis
        $.ajax(
        {
            url: 'https://api.github.com/emojis',
            async: false
        }).then(function(data)
        {
            window.emojis = Object.keys(data);
            window.emojiUrls = data;
        });

        //init emoji example
        $(".js-hint2emoji").summernote(
        {
            height: 100,
            toolbar: false,
            placeholder: 'type starting with : and any alphabet',
            hint:
            {
                match: /:([\-+\w]+)$/,
                search: function(keyword, callback)
                {
                    callback($.grep(emojis, function(item)
                    {
                        return item.indexOf(keyword) === 0;
                    }));
                },
                template: function(item)
                {
                    var content = emojiUrls[item];
                    return '<img src="' + content + '" width="20" /> :' + item + ':';
                },
                content: function(item)
                {
                    var url = emojiUrls[item];
                    if (url)
                    {
                        return $('<img />').attr('src', url).css('width', 20)[0];
                    }
                    return '';
                }
            }
        });

        //init mentions example
        $(".js-hint2mention").summernote(
        {
            height: 100,
            toolbar: false,
            placeholder: "type starting with @",
            hint:
            {
                mentions: ['jayden', 'sam', 'alvin', 'david'],
                match: /\B@(\w*)$/,
                search: function(keyword, callback)
                {
                    callback($.grep(this.mentions, function(item)
                    {
                        return item.indexOf(keyword) == 0;
                    }));
                },
                content: function(item)
                {
                    return '@' + item;
                }
            }
        });

    });

</script>
<script type="text/javascript">


    $(".show-table").click(function(){
        $(".hide-table").show();
    });
    
</script>

    <script>
        //_fnFeatureHtmlLength();
        $(document).ready(function () {
            // Setup - add a text input to each footer cell
            $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
            $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
                var title = $(this).text();
                $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search '  + '" />');

                $('input', this).on('keyup change', function () {
                    if (table.column(i).search() !== this.value) {
                        table
                            .column(i)
                            .search(this.value)
                            .draw();
                    }
                });
            });
            var table = $('#dt-basic-example').DataTable(
                {
                    // responsive: true,
                    orderCellsTop: true,
                    fixedHeader: true,
                    lengthChange: true,

                    dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

                    buttons: [
                        {
                            extend: 'pageLength',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'colvis',
                            text: 'Column Visibility',
                            titleAttr: 'Col visibility',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            titleAttr: 'Generate PDF',
                            className: 'btn-outline-danger btn-sm mr-1'
                        },
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            titleAttr: 'Generate Excel',
                            className: 'btn-outline-success btn-sm mr-1'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            titleAttr: 'Generate CSV',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'copyHtml5',
                            text: 'Copy',
                            titleAttr: 'Copy to clipboard',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            titleAttr: 'Print Table',
                            className: 'btn-outline-primary btn-sm'
                        }
                    ],
                });
            $('.js-thead-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
            });

            $('.js-tbody-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
            });

        });

    </script>


<!-- Filter -->
<script>
   $(document).ready(function(){
   
    fetch_customer_data();
   
    function fetch_customer_data(from = '',to = '',coin = '',account = '',type = '',cost = '',user = '',id='')
    {
     $.ajax({
      url:'Cost_Centers_ReportFilter',
      method:'GET',
      data:{from:from,to:to,coin:coin,account:account,type:type,cost:cost,user:user,id:id},
      dataType:'json',
      success:function(data)
      {
          

       $('.Data').append(data.table_data);
       $('.TD').html(data.totalD);
       $('.TC').html(data.totalC);
       $('.NUM').html(data.totalN);
       $('.DIF').html(data.dif);
          

          
      }
     })
    }
    
  $(document).on('change', '#from', function(){
     var from = $(this).val();     
     var to = $('#to').val();     
     var coin = $('#coin').val();     
     var account = $('#account').val();     
     var type = $('#type').val();     
     var cost = $('#cost').val();     
     var user = $('#user').val();   
     var id ='';  
          $('.Data').empty();     
     fetch_customer_data(from,to,coin,account,type,cost,user,id);
    });
       
  $(document).on('change', '#user', function(){
     var user = $(this).val();     
     var to = $('#to').val();     
     var coin = $('#coin').val();     
     var account = $('#account').val();     
     var type = $('#type').val();     
     var cost = $('#cost').val();     
     var from = $('#from').val();     
       var id ='';  
          $('.Data').empty();     
     fetch_customer_data(from,to,coin,account,type,cost,user,id);
    });   

 $(document).on('change', '#to', function(){
     var to = $(this).val();     
     var from = $('#from').val();     
     var coin = $('#coin').val();     
     var account = $('#account').val();     
     var type = $('#type').val();     
     var cost = $('#cost').val();     
     var user = $('#user').val();     
        var id ='';  
          $('.Data').empty();     
     fetch_customer_data(from,to,coin,account,type,cost,user,id);
    });

 $(document).on('change', '#coin', function(){
     var coin = $(this).val();     
     var to = $('#to').val();     
     var from = $('#from').val();     
     var account = $('#account').val();     
     var type = $('#type').val();     
     var cost = $('#cost').val();     
     var user = $('#user').val();    
       var id ='';  
          $('.Data').empty();     
     fetch_customer_data(from,to,coin,account,type,cost,user,id);
    });

 $(document).on('change', '#account', function(){
     var account = $(this).val();     
     var to = $('#to').val();     
     var coin = $('#coin').val();     
     var from = $('#from').val();     
     var type = $('#type').val();     
     var cost = $('#cost').val();     
     var user = $('#user').val();     
   var id ='';  
          $('.Data').empty();     
     fetch_customer_data(from,to,coin,account,type,cost,user,id);
    });
     
 $(document).on('change', '#type', function(){
     var type = $(this).val();     
     var to = $('#to').val();     
     var coin = $('#coin').val();     
     var account = $('#account').val();     
     var from = $('#from').val();     
     var cost = $('#cost').val();     
     var user = $('#user').val();     
       var id ='';  
          $('.Data').empty();     
     fetch_customer_data(from,to,coin,account,type,cost,user,id);
    });       
    
 $(document).on('change', '#cost', function(){
     var cost = $(this).val();     
     var to = $('#to').val();     
     var coin = $('#coin').val();     
     var account = $('#account').val();     
     var type = $('#type').val();     
     var from = $('#from').val();     
     var user = $('#user').val();   
        var id ='';  
          $('.Data').empty();     
     fetch_customer_data(from,to,coin,account,type,cost,user,id);
    });  
       
       
     $(document).on('click', '#load_more_button', function(){
     var id = $(this).data('id');
    var cost = $('#cost').val();     
     var to = $('#to').val();     
     var coin = $('#coin').val();     
     var account = $('#account').val();     
     var type = $('#type').val();     
     var from = $('#from').val();     
     var user = $('#user').val();     
  $('#load_more_button').html('<b>Loading...</b>');
 fetch_customer_data(from,to,coin,account,type,cost,user,id);
 });     
       
       
   });
</script>


<!-- Search for Bond -->
<script>
   $(document).ready(function(){
   
    fetch_customer_data();
   
    function fetch_customer_data(Code = '',Type = '')
    {
     $.ajax({
      url:'GenralDailyFilterBond',
      method:'GET',
      data:{Code:Code,Type:Type},
      dataType:'json',
      success:function(data)
      {
       $('.BILL').html(data.table_data);
      }
     })
    }
    
  $(document).on('keyup', '#Bond_Code', function(){
     var Code = $(this).val();     
     var Type = $('#Bond_Type').val();         
     fetch_customer_data(Code,Type);
    });
       
      $(document).on('change', '#Bond_Type', function(){
     var Type = $(this).val();     
     var Code = $('#Bond_Code').val();         
     fetch_customer_data(Code,Type);
    });   
  
       
   });
</script>


<?php $__env->stopPush(); ?>


<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\erp\resources\views/admin/AccountsReports/Cost_Centers_Report.blade.php ENDPATH**/ ?>
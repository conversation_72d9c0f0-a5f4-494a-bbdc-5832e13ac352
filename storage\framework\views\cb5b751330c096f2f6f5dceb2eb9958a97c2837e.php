<?php $__env->startSection('content'); ?>
<?php
use App\Models\StoresDefaultData;
use App\Models\ProductTypeDefault;
use App\Models\DefaultDataShowHide;
$Def=StoresDefaultData::orderBy('id','desc')->first();
$DefSHOW=DefaultDataShowHide::orderBy('id','desc')->first();
$Types=ProductTypeDefault::all();
?>
<title><?php echo e(trans('admin.Add_Items')); ?></title>
<style>
    #dvPreview ,#dvPreview2
{
    filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=image);
    min-height: 60px;
    min-width: 60px;
    display: none;
}
#dvPreview img,#dvPreview2 img{
    height:100%;
    width:100%;
}
.text{
    font-size:15px;
    color:#d4a1cc;
    animation: first 2s linear 2s infinite alternate;
}
@keyframes  first{from{font-size:15px;color:#d4a1cc}
to {font-size:18px;color:orange}}

.inputplac::placeholder{
    color:white;
}
.label{
    color:black !important;
}
.buttonPlus{
    background:white;
    color:#886ab5;
}
.buttonPlus:hover {
    background:#886ab5;
    color:white;
}
.table-color1 {
   background:#d4a1cc; 
}
.TableColor{
background:#1225c880;
}
</style>
<main id="js-page-content" role="main" class="page-content">
   <form action="<?php echo e(url('PostAddProduct')); ?>" method="post" enctype="multipart/form-data">
      <?php echo csrf_field(); ?>   
        <?php echo view('honeypot::honeypotFormFields'); ?>
      <ol class="breadcrumb page-breadcrumb">
         <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Stores')); ?></a></li>
         <li class="breadcrumb-item active"><?php echo e(trans('admin.Add_Items')); ?></li>
         <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
            class="js-get-date"></span></li>
      </ol>
                          <style>
      
    
       .Hei{
          background:#886ab5;
           color:WHITE;
          
           padding:10px;
        margin-bottom:20px;
        box-shadow: 0 2px 6px 0 rgba(136, 106, 181, 0.5);
       }
       .wei{
                background:#886ab5;
           color:WHITE;
       
           padding:10px;
           margin-bottom:20px;
           box-shadow: 0 2px 6px 0 rgba(136, 106, 181, 0.5);
       }
       .BlogsH{
           display:flex;
           text-align:center;
               flex-direction: column;
       }
   </style>




     
       <p class="container text-center " style="margin-bottom:10px;">
  <a class="btn btn-primary" data-toggle="collapse" href="#multiCollapseExample1" role="button" aria-expanded="true" aria-controls="multiCollapseExample1"> Size Of Special Offers</a>
  <button class="btn btn-primary" type="button" data-toggle="collapse" data-target="#multiCollapseExample2" aria-expanded="false" aria-controls="multiCollapseExample2"> Size Of New Arrival</button>
  <button class="btn btn-primary" type="button" data-toggle="collapse" data-target="#multiCollapseExample3" aria-expanded="false" aria-controls="multiCollapseExample3"> Size Of Best Sellers</button>
</p>
<div class="row">
  <div class="col">
    <div class="collapse " id="multiCollapseExample1">
      <div class="card card-body">
            <div  class="col-lg-12 text-center Hei"><h2 class="BlogsH"> Special Offers</h2>width:355px^Height:250px</div>
      </div>
    </div>
  </div>
  <div class="col">
    <div class="collapse " id="multiCollapseExample2">
      <div class="card card-body">
                <div class="col-lg-12 text-center wei"><h2 class="BlogsH">New Arrival</h2>width:450px^Height:450px</div>
      </div>
    </div>
  </div>
   <div class="col">
    <div class="collapse " id="multiCollapseExample3">
      <div class="card card-body">
                 <div class="col-lg-12 text-center wei"><h2 class="BlogsH">Best Sellers</h2>width:306px^Height:306px</div>
      </div>
    </div>
  </div>
</div>          
      <div class="row">
         <div class="col-xl-12">
            <div  class="panel">
               <div class="panel-container show">
                  <div class="panel-content">
                     <div class="panel-container show">
                        <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>  
                        <div class="panel-content">
                           <ul class="nav nav-tabs" role="tablist">
                              <li class="nav-item">
                                 <a class="nav-link active" data-toggle="tab" href="#tab_borders_icons-1" role="tab"><i class="fal fa-home mr-1"></i>    <?php echo e(trans('admin.Main_Data')); ?></a>
                              </li>
                              <li class="nav-item">
                                 <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-2" role="tab"><i class="fal fa-user mr-1"></i>    <?php echo e(trans('admin.Sub_Data')); ?></a>
                              </li>
                               
                                       <li class="nav-item">
                                 <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-3" role="tab"><i class="fal fa-user mr-1"></i>    <?php echo e(trans('admin.Additions')); ?></a>
                              </li>
                           </ul>
                           <div class="tab-content border border-top-0 p-3">
                              <div class="tab-pane fade show active" id="tab_borders_icons-1" role="tabpanel">
                                 <div class="form-row">
                                    <div class="form-group col-md-2">
                                       <label class="form-label" for=""> 
                                       <?php echo e(trans('admin.Product_Type')); ?>    
                                       </label><span class="strick">*</span>
         <select class="select2 form-control w-100" id="P_Type" name="P_Type" onchange="Assembly()" required>
                                          <option value=""><?php echo e(trans('admin.Product_Type')); ?> </option>
                        <?php $__currentLoopData = $Types; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                     <option value="<?php echo e($type->Type); ?>" <?php if($Def->Type == $type->Type): ?> selected <?php endif; ?>>
                         
                          <?php if($type->Type == "Completed"): ?> 
                                                        <?php echo e(trans('admin.Completed')); ?>

                                                    <?php elseif($type->Type == "Raw"): ?>
                                                         <?php echo e(trans('admin.Raw')); ?>

                                                    <?php elseif($type->Type == "Service"): ?>
                                                         <?php echo e(trans('admin.Service')); ?>

                                                    <?php elseif($type->Type == "Assembly"): ?>
                                                         <?php echo e(trans('admin.Assembly')); ?>

                                                    <?php elseif($type->Type == "Industrial"): ?>
                                                         <?php echo e(trans('admin.Industrial')); ?>

                                                    <?php elseif($type->Type == "Single_Variable"): ?>
                                                         <?php echo e(trans('admin.Single_Variable')); ?>

                                                    <?php elseif($type->Type == "Duble_Variable"): ?>
                                                         <?php echo e(trans('admin.Duble_Variable')); ?>

                                                    <?php elseif($type->Type == "Subscribe"): ?>
                                                        <?php echo e(trans('admin.Subscribe')); ?>

                                                    <?php elseif($type->Type == "Serial"): ?>
                                                         <?php echo e(trans('admin.Serial')); ?>                                   
                         <?php elseif($type->Type == "Petroll"): ?>
                                                         <?php echo e(trans('admin.Petroll')); ?>    
                         <?php elseif($type->Type == "Variable_Aggregate"): ?>
                                                         <?php echo e(trans('admin.Variable_Aggregate')); ?>    
                         <?php elseif($type->Type == "Additions"): ?>
                                                         <?php echo e(trans('admin.Additions')); ?>

                                                    <?php endif; ?>      
                        
                        
                        </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>            
                                       </select>
                                    </div>
                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Product_Ar_Name')); ?> 
                                       </label><span class="strick">*</span>
      <input type="text" name="P_Ar_Name" value="<?php echo e(old('P_Ar_Name')); ?>" placeholder="<?php echo e(trans('admin.Product_Ar_Name')); ?> "  class="form-control" onkeyup="CheckNAME()" id="ARNAME" required>
                                    </div>
                        <span style="display: none; color: red ;    position: absolute; margin-right: 260px;margin-top: 63px;" id="AlertName">
                           <?php echo e(trans('admin.ThisNameAlreadyExist')); ?>

                                     </span>             
                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput">  <?php echo e(trans('admin.Product_En_Name')); ?></label>
         <input type="text" name="P_En_Name" value="<?php echo e(old('P_En_Name')); ?>" placeholder="<?php echo e(trans('admin.Product_En_Name')); ?>" onkeyup="CheckNAMEENGLISH()" id="ENNAME"  class="form-control">
                                    </div>
                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="">  <?php echo e(trans('admin.Brand')); ?> </label>
                                       <select class="select2 form-control w-100" name="Brand">
                                          <option value=""><?php echo e(trans('admin.Brand')); ?></option>
                                          <?php $__currentLoopData = $Brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                          <option value="<?php echo e($brand->id); ?>">
                                       <?php echo e(app()->getLocale() == 'ar' ?$brand->Name :$brand->NameEn); ?>        
                                           </option>
                                          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                       </select>
                                    </div>
                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="">  <?php echo e(trans('admin.Group')); ?>  </label><span class="strick">*</span>
                                       <select class="select2 form-control w-100" name="Group" required>
                                          <option value=""><?php echo e(trans('admin.Group')); ?></option>
                                          <?php $__currentLoopData = $ItemsGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                 <option value="<?php echo e($group->id); ?>" <?php if($Def->Group == $group->id): ?> selected <?php endif; ?>>
                                         <?php echo e(app()->getLocale() == 'ar' ?$group->Name :$group->NameEn); ?>   
                                           
                                           </option>
                                          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                       </select>
                                    </div>
                                    <div class="form-group col-md-2 mb-2">
                                      
                                     <label><i class="fal fa-image"></i> <?php echo e(trans('admin.Image')); ?>  </label>  
                                     <input id="fileupload" type="file" name="Image" >

                                    </div>
                                     
  
                                    <div class="form-group col-md-12 mb-3" style="display: none" id="EndDate">
                                        <div class="row">
                                        <div class="col-md-6">
                                       <label class="form-label" for="simpleinput">  <?php echo e(trans('admin.Sub_Cost')); ?></label>
                                       <input type="number" step="any" name="Sub_Cost" value="<?php echo e(old('Sub_Cost')); ?>" placeholder="<?php echo e(trans('admin.Sub_Cost')); ?> "  class="form-control">
                                       </div>
                                        <div class="col-md-6">
                                       <label class="form-label" for="">  <?php echo e(trans('admin.Subscribe_Type')); ?>  </label>
                                       <select class="select2 form-control w-100" name="subscribe_type" >
                                          <option value=""><?php echo e(trans('admin.Subscribe_Type')); ?></option>
                                          <?php $__currentLoopData = $SubscribeTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sub): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                          <option value="<?php echo e($sub->id); ?>">
                                    <?php echo e(app()->getLocale() == 'ar' ?$sub->Name :$sub->NameEn); ?>             
                                           </option>
                                          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                       </select>
                                       </div>
                                       </div>
                                    </div>
                                    <!-- Assembly -->       
                                    <div id="HideAssembly" style="display: none" class="col-md-12 mb-12">
                                       <div class="form-group col-md-12 mb-12">
                                          <div class="input-items">
                                             <input type="text" id="search" class="form-control" placeholder="<?php echo e(trans('admin.Search_For_Products')); ?> ">
                                             <i class="fal fa-barcode-alt" style="margin-top: -25px;position: absolute;margin-right: 967px;"></i>
                                          </div>
                                       </div>
                                       <table class="table table-bordered table-hover table-striped w-100 hide-products-table TableColor">
                                          <thead>
                                             <tr>
                                                <th><?php echo e(trans('admin.Name')); ?></th>
                                                <th><?php echo e(trans('admin.Units')); ?></th>
                                                <th><?php echo e(trans('admin.Qty')); ?></th>
                                                <th><?php echo e(trans('admin.Price')); ?> </th>
                                                <th><?php echo e(trans('admin.Total')); ?></th>
                                                <th><?php echo e(trans('admin.Actions')); ?></th>
                                             </tr>
                                          </thead>
                                          <tbody id="data">
                                          </tbody>
                                       </table>
                                       <table class="table table-bordered table-hover table-striped w-100 hide-products-table TableColor"  >
                                          <thead>
                                             <tr>
                                                <th><?php echo e(trans('admin.Name')); ?></th>
                                                <th><?php echo e(trans('admin.Barcode')); ?></th>
                                                <th><?php echo e(trans('admin.Units')); ?></th>
                                                <th><?php echo e(trans('admin.Qty')); ?></th>
                                                <th><?php echo e(trans('admin.Price')); ?> </th>
                                                <th><?php echo e(trans('admin.Total')); ?></th>
                                                <th><?php echo e(trans('admin.Actions')); ?></th>
                                             </tr>
                                          </thead>
                                          <tbody id="data-dt">
                                          </tbody>
                                       </table>
                                        <div class="row">
                                     <label><?php echo e(trans('admin.AssemblyTotal')); ?></label>
                                    <input type="text" class="form-control" id="AssemblyTotal" value="0" readonly>     
                                     </div>    
                                        
                                    </div>
                                 
                                    <!-- Vira One -->       
                                    <div id="HideViraOne" style="display: none" class="col-md-3 mb-3">
                                       <div class="form-group ">
                                          <label class="form-label" for=""><?php echo e(trans('admin.Virable')); ?> </label>
                                          <select class="select2 form-control w-100" id="VOne">
                                             <option value=""><?php echo e(trans('admin.Virable')); ?></option>
                                             <?php $__currentLoopData = $Virables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vir): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                             <option value="<?php echo e($vir->id); ?>">
                                              <?php echo e(app()->getLocale() == 'ar' ?$vir->Name :$vir->NameEn); ?>        
                                              </option>
                                             <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                          </select>
                                       </div>
                                       <div class="padding-tables" style="display: none">
                                          <table id="color" class="table table-bordered table-hover table-striped w-100">
                                             <thead class="bg-highlight">
                                                <tr>
                                                   <th><?php echo e(trans('admin.Virable')); ?></th>
                                                   <th><?php echo e(trans('admin.Cost')); ?></th>
                                                </tr>
                                             </thead>
                                             <tbody class="ViraOne">
                                             </tbody>
                                          </table>
                                       </div>
                                    </div>
                                    <!-- Vira Two -->       
                                    <div id="HideViraTwo" style="display: none" class="col-md-6 mb-6">
                                       <div class="form-row">
                                          <div class="form-group col-lg-6 ">
                                             <label class="form-label" for=""><?php echo e(trans('admin.Virable')); ?> </label>
                                             <select class="select2 form-control w-100" id="VTwo">
                                                <option value=""><?php echo e(trans('admin.Virable')); ?></option>
                                                <?php $__currentLoopData = $Virables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vir): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($vir->id); ?>">
                                                    <?php echo e(app()->getLocale() == 'ar' ?$vir->Name :$vir->NameEn); ?> 
                                                 </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                             </select>
                                          </div>
                                          <div class="form-group col-lg-6 ">
                                             <label class="form-label" for=""><?php echo e(trans('admin.Virable')); ?> </label>
                                             <select class="select2 form-control w-100" id="VTwoo">
                                                <option value=""><?php echo e(trans('admin.Virable')); ?></option>
                                                <?php $__currentLoopData = $Virables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vir): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($vir->id); ?>">   <?php echo e(app()->getLocale() == 'ar' ?$vir->Name :$vir->NameEn); ?> </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                             </select>
                                          </div>
                                          <div class="alert alert-danger" id="AlertVira" style="display: none">
                                             <?php echo e(trans('admin.Can_not_Choice_Same_Virables')); ?>    
                                          </div>
                                       </div>
                                       <div style="display: none">
                                          <div class="padding-tables" id="TabV2">
                                             <table id="variables" class="table table-bordered table-hover">
                                             </table>
                                          </div>
                                       </div>
                                    </div>
                                     
                                       <!-- Variable_Aggregate -->       
                                    <div id="HideVariable_Aggregate" style="display: none" class="col-md-12 mb-12">
                                       <div class="form-group col-md-12 mb-12">
                                          <div class="input-items">
        <input style="background:#57487a;color: white;" type="text" id="searchVariableAggregate" class="form-control inputplac" placeholder="<?php echo e(trans('admin.Search_For_Products')); ?> ">
                                        </div>
                                       </div>
                                       <table class="table table-bordered table-hover table-striped w-100 hide-products-table">
                                          <thead>
                                             <tr>
                                                <th><?php echo e(trans('admin.Product_Name')); ?></th>
                                                <th><?php echo e(trans('admin.Product_Code')); ?></th>
                                                <th><?php echo e(trans('admin.Unit')); ?></th>
                                                <th><?php echo e(trans('admin.Actions')); ?></th>
                                             </tr>
                                          </thead>
                                          <tbody id="dataVariableAggregate">
                                          </tbody>
                                       </table>
                                       <table class="table table-bordered table-hover table-striped w-100 hide-products-table">
                                          <thead>
                                             <tr>
                                            <th><?php echo e(trans('admin.Product_Name')); ?></th>
                                                <th><?php echo e(trans('admin.Product_Code')); ?></th>
                                                <th><?php echo e(trans('admin.Unit')); ?></th>
                                                <th><?php echo e(trans('admin.Actions')); ?></th>
                                             </tr>
                                          </thead>
                                          <tbody id="data-dt-VariableAggregate">
                                          </tbody>
                                       </table>
                                        
                                        <div class="row" style="background: #d4a1cc;padding: 15px;">
                                        <div class="col-md-3 DL">
                                    <label><?php echo e(trans('admin.Virable')); ?></label>        
                                    <select class=" form-control" id="PriceVASelect"  onchange="SubSayPrice()">
                                            
                                            </select>        
                                        </div>
                                            
                                                   <div class="col-md-3 DL">
                                    <label><?php echo e(trans('admin.Virable')); ?></label>        
                                    <select class="select2 form-control" id="PriceSubVASelect">
                                            
                                            </select>        
                                        </div>
                                            
                                          <div class="col-md-3 DL">
                                    <label><?php echo e(trans('admin.Price')); ?></label>        
                                  <input type="number" step="any" class="form-control" id="PriceVA">       
                                        </div>                                                  
                                            
                                            <div class="col-md-3 DL">
                                    <label><?php echo e(trans('admin.Offer_Price')); ?></label>        
                                  <input type="number" step="any" class="form-control" id="OfferPriceVA">       
                                        </div>        
                                      
                                              <div class="col-md-12" style="text-align:left;">
                                        <button class="btn btn-primary buttonPlus"  onclick="AddPriceVA()" type="button"><i class="fal fa-plus"></i></button>          
                                            </div>
                                        
                                        </div>
                                        
                                              <table class="table table-bordered table-hover table-striped w-100 hide-products-table " style="background: #d4a1cc;">
                                          <thead>
                                             <tr>
                                            <th><?php echo e(trans('admin.Virable')); ?></th>
                                                <th><?php echo e(trans('admin.Virable')); ?></th>
                                                <th><?php echo e(trans('admin.Price')); ?></th>
                                                <th><?php echo e(trans('admin.Offer_Price')); ?></th>
                                                <th><?php echo e(trans('admin.Actions')); ?></th>
                                             </tr>
                                          </thead>
                                          <tbody id="data-price-VariableAggregate">
                                          </tbody>
                                       </table>
                                        
                                        
                                    </div>
                                     
                                     
                                     
                                 </div>
                                 <div class="row">
                                    <div class="col-xl-12">
                                       <div id="panel-1" class="panel">
                                           <!--   <div class="panel-hdr">
                                             <h2>
                                                <?php echo e(trans('admin.Add_Items')); ?>   
                                             </h2>
                                         <div class="panel-toolbar">
                                                <button class="btn btn-primary btn-sm" data-toggle="dropdown">Table
                                                Style</button>
                                                <div
                                                   class="dropdown-menu dropdown-menu-animated dropdown-menu-right position-absolute pos-top">
                                                   <button class="dropdown-item active" data-action="toggle"
                                                      data-class="table-bordered" data-target="#dt-basic-example"> Bordered
                                                   Table </button>
                                                   <button class="dropdown-item" data-action="toggle" data-class="table-sm"
                                                      data-target="#dt-basic-example"> Smaller Table </button>
                                                   <button class="dropdown-item" data-action="toggle" data-class="table-dark"
                                                      data-target="#dt-basic-example"> Table Dark </button>
                                                   <button class="dropdown-item active" data-action="toggle"
                                                      data-class="table-hover" data-target="#dt-basic-example"> Table Hover
                                                   </button>
                                                   <button class="dropdown-item active" data-action="toggle"
                                                      data-class="table-stripe" data-target="#dt-basic-example"> Table
                                                   Stripped </button>
                                                   <div class="dropdown-divider m-0"></div>
                                                   <div class="dropdown-multilevel dropdown-multilevel-left">
                                                      <div class="dropdown-item">
                                                         tbody color
                                                      </div>
                                                      <div class="dropdown-menu no-transition-delay">
                                                         <div class="js-tbody-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                            style="width: 15.9rem; padding: 0.5rem">
                                                            <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                               class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                               class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                               class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                               class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                               class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                               class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                               class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                               class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                               class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-100"
                                                               class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-200"
                                                               class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-300"
                                                               class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-400"
                                                               class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-500"
                                                               class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-600"
                                                               class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-700"
                                                               class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-800"
                                                               class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-900"
                                                               class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-100"
                                                               class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-200"
                                                               class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-300"
                                                               class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-400"
                                                               class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-500"
                                                               class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-600"
                                                               class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-700"
                                                               class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-800"
                                                               class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-900"
                                                               class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                               class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                               class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                               class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                               class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                               class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                               class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                               class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                               class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                               class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                               class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                               class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                               class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                               class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                               class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                               class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                               class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                               class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                               class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                               class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                               class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                               class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                               class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                               class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                               class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                               class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                               class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                               class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg=""
                                                               class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                         </div>
                                                      </div>
                                                   </div>
                                                   <div class="dropdown-multilevel dropdown-multilevel-left">
                                                      <div class="dropdown-item">
                                                         thead color
                                                      </div>
                                                      <div class="dropdown-menu no-transition-delay">
                                                         <div class="js-thead-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                            style="width: 15.9rem; padding: 0.5rem">
                                                            <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                               class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                               class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                               class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                               class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                               class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                               class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                               class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                               class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                               class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-100"
                                                               class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-200"
                                                               class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-300"
                                                               class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-400"
                                                               class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-500"
                                                               class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-600"
                                                               class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-700"
                                                               class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-800"
                                                               class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-900"
                                                               class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-100"
                                                               class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-200"
                                                               class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-300"
                                                               class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-400"
                                                               class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-500"
                                                               class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-600"
                                                               class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-700"
                                                               class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-800"
                                                               class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-900"
                                                               class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                               class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                               class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                               class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                               class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                               class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                               class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                               class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                               class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                               class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                               class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                               class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                               class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                               class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                               class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                               class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                               class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                               class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                               class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                               class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                               class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                               class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                               class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                               class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                               class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                               class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                               class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                               class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg=""
                                                               class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                               style="margin:1px"></a>
                                                         </div>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>  
                                          </div> -->
                                          <div class="panel-container show">
                                             <div class="panel-content">
                                                 <div id="mobile-overflow">
                                        <span class="text"><?php echo e(trans('admin.ArabicCode')); ?></span>             
                                                <table class="table table-bordered table-hover table-striped th-width mt-2 table-color1">
                                                   <thead>
                                                      <tr>
                                                         <th><?php echo e(trans('admin.Unit')); ?></th>
                                                         <th><?php echo e(trans('admin.Rate')); ?></th>
                                                         <th><?php echo e(trans('admin.Barcode')); ?></th>
                                                         <th><?php echo e(trans('admin.Price_One')); ?></th>
                                                         <th><?php echo e(trans('admin.Price_Two')); ?></th>
                                                         <th><?php echo e(trans('admin.Price_Three')); ?></th>
                                                         <th><?php echo e(trans('admin.Actions')); ?></th>
                                                      </tr>
                                                   </thead>
                                                   <tbody>
                                                      <tr>
                                                         <td>
                                                            <div class="form-group">
                                          <select class="select2 form-control w-100" id="unit" onchange="PLUS()">
                                                                  <option value=""><?php echo e(trans('admin.Unit')); ?></option>
                                                                  <?php $__currentLoopData = $Units; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $uni): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                              <option value="<?php echo e($uni->id); ?>" <?php if($Def->Unit == $uni->id): ?> selected <?php endif; ?>>
                                               <?php echo e(app()->getLocale() == 'ar' ?$uni->Name :$uni->NameEn); ?>   
                                              </option>
                                                                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                               </select>
                                                               <input type="hidden" id="UnitName" >                               
                                                               <input type="hidden" id="UnitID" >                               
                                                            </div>
                                                         </td>
                                                         <td>
                                                            <div class="form-group">
                                                               <input type="number" step="any" id="rate" class="form-control" onkeyup="PLUS()" onclick="PLUS()" value="1">
                                                            </div>
                                                         </td>
                                                         <td>
                                                            <div class="form-group" style="position:relative;">
                          <input type="text" id="code" class="form-control"  onkeyup="PLUS()" >
                                                               <i class="fal fa-barcode" onclick="Genrte()" style="position: absolute;top:13px;left:3px; "></i>                
                                                            </div>
                                                         </td>
                                                         <td>
                                                            <div class="form-group">
                                                               <input type="number" step="any" id="price1" class="form-control" onkeyup="PLUS()" onclick="PLUS()">
                                                            </div>
                                                         </td>
                                                         <td>
                                                            <div class="form-group">
                                                               <input type="number" step="any" id="price2" class="form-control" onkeyup="PLUS()" onclick="PLUS()">
                                                            </div>
                                                         </td>
                                                         <td>
                                                            <div class="form-group">
                                                               <input type="number" step="any" id="price3" class="form-control" onkeyup="PLUS()" onclick="PLUS()">
                                                            </div>
                                                         </td>
                                                         <td>
                                                            <button type="button" onclick="InsertData()" class="btn btn-default" id="add-data" style="display: none"><i class="fal fa-plus"></i></button>
                                                         </td>
                                                      </tr>
                                                   </tbody>
                                                </table>
                                                </div>
                                                <!-- datatable start -->
                                                 <div id="mobile-overflow">
                                                <table id="samble"   class="table table-bordered table-hover table-striped w-100 mobile-width table-color2">
                                                   <thead>
                                                      <tr>
                                                         <th><?php echo e(trans('admin.Unit')); ?></th>
                                                         <th><?php echo e(trans('admin.Rate')); ?></th>
                                                         <th><?php echo e(trans('admin.Barcode')); ?></th>
                                                         <th><?php echo e(trans('admin.Price_One')); ?></th>
                                                         <th><?php echo e(trans('admin.Price_Two')); ?></th>
                                                         <th><?php echo e(trans('admin.Price_Three')); ?></th>
                                                         <th><?php echo e(trans('admin.Default_Unit')); ?></th>
                                                         <th><?php echo e(trans('admin.Actions')); ?></th>
                                                      </tr>
                                                   </thead>
                                                   <tbody id="data-dt-first">
                                                   </tbody>
                                                </table>
                                                </div>
                                                <!-- datatable end -->
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                              <div class="tab-pane fade" id="tab_borders_icons-2" role="tabpanel">
                                 <div class="form-row">
                                     
                                     
                                           <div class="form-group col-md-2 mb-2">
                                      
                                     <label><i class="fal fa-image"></i> <?php echo e(trans('admin.Image2')); ?>  </label>  
                                     <input id="fileupload" type="file" name="Image2" >

                                    </div>
                                    
                                     
                                     
                                      <div class="form-group col-md-2">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.If_Offer')); ?> </label>
                                       <select class="select2 form-control w-100" name="Offer" onchange="ShowOffers()" id="IFOffer">
                                          <option value=""><?php echo e(trans('admin.If_Offer')); ?></option>
                                          <option value="1"><?php echo e(trans('admin.Yes')); ?></option>
                                          <option value="0" selected><?php echo e(trans('admin.No')); ?></option>
                                       </select>
                                    </div> 
                                                          
                                                      <div class="form-group col-md-2" id="OFF1" style="display: none">
                 <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.OfferPrice')); ?> </label>
                    <input type="number" step='any' class="form-control" name="OfferPrice">
                                    </div>   
                                     
                                                                              
                                                      <div class="form-group col-md-2" id="OFF2" style="display: none">
                 <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Offer_Start_Date')); ?> </label>
                    <input type="date" step='any' class="form-control" name="Offer_Start_Date">
                                    </div>   
                                     
                                                                                 
                                                      <div class="form-group col-md-2" id="OFF3" style="display: none">
                 <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Offer_End_Date')); ?> </label>
                    <input type="date" step='any' class="form-control" name="Offer_End_Date">
                                    </div>   
                                     
                                     <div class="form-group col-md-2">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Tax')); ?> </label>
                                       <select class="select2 form-control w-100" name="Tax" id="TAX">
                                          <option value=""><?php echo e(trans('admin.Tax')); ?></option>
                                          <?php $__currentLoopData = $Taxes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tax): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
             <option value="<?php echo e($tax->id); ?>" <?php if($Def->Tax == $tax->id): ?> selected <?php endif; ?>>
                                     <?php echo e(app()->getLocale() == 'ar' ?$tax->Name :$tax->NameEn); ?>        
                                           </option>
                                          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                                       </select>
                                    </div>  
                                     
                                    <div class="form-group col-md-2">
                 <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Price_With_Tax')); ?> </label>
      <input type="number" step='any' class="form-control" id="PriceWithTax" value="0">
                                    </div>     
                                     
                                     <div class="form-group col-md-2">
                 <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.OriginalPrice')); ?> </label>
      <input type="number" disabled step='any' class="form-control" id="OriginalPrice" value="0">
                                    </div>   
                                     
                                                             
            
                                     
                                     
                                     
                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Maximum_Sales_Qty')); ?>  </label>
                                       <input type="number" name="Maximum_Sales_Qty" value="<?php echo e(old('Maximum_Sales_Qty')); ?>" class="form-control">
                                    </div>               
                                     
                                     
                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Minimum')); ?>  </label>
                                       <input type="number" name="Minimum" value="<?php echo e(old('Minimum')); ?>" class="form-control">
                                    </div>
                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Maximum')); ?>  </label>
                                       <input type="number" name="Maximum" value="<?php echo e(old('Maximum')); ?>" class="form-control">
                                    </div>
                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Length')); ?>  </label>
                                       <input type="number" step="any" name="Length" value="<?php echo e(old('Length')); ?>" class="form-control">
                                    </div>
                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Width')); ?>   </label>
                                       <input type="number" step="any" name="Width" value="<?php echo e(old('Width')); ?>" class="form-control">
                                    </div>
                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Height')); ?>   </label>
                                       <input type="number" step="any" name="Height" value="<?php echo e(old('Height')); ?>" class="form-control">
                                    </div>          
                                     <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Thickness')); ?>   </label>
                                       <input type="number" step="any" name="Thickness" value="<?php echo e(old('Thickness')); ?>" class="form-control">
                                    </div>
                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Weight')); ?>   </label>
                                       <input type="number" step="any" name="Weight" value="<?php echo e(old('Weight')); ?>" class="form-control">
                                    </div>
                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Saller_Point')); ?>   </label>
                                       <input type="number" step="any" name="Saller_Point" value="<?php echo e(old('Saller_Point')); ?>" class="form-control">
                                    </div>
                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Customer_Point')); ?>  </label>
                                       <input type="number" step="any" name="Customer_Point" value="<?php echo e(old('Customer_Point')); ?>" class="form-control">
                                    </div>
                                          <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Guess_Price')); ?>  </label>
                                       <input type="number" step="any" name="Guess_Price" value="<?php echo e(old('Guess_Price')); ?>" class="form-control">
                                    </div>
                                     
                                     
                                                 <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput">Cas No  </label>
                                       <input type="text"  name="Cas_No" value="<?php echo e(old('Cas_No')); ?>" class="form-control">
                                    </div>
                                                 <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput">HSN  </label>
                                       <input type="text"  name="HSN" value="<?php echo e(old('HSN')); ?>" class="form-control">
                                    </div>
                                                 <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput">Uni Code  </label>
                                       <input type="text"  name="Uni_Code" value="<?php echo e(old('Uni_Code')); ?>" class="form-control">
                                    </div>
                                     
                                                      <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Origin_Number')); ?>  </label>
                                       <input type="text"  name="Origin_Number" value="<?php echo e(old('Origin_Number')); ?>" class="form-control">
                                    </div>
                                                              <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Origin_Country')); ?>  </label>
                                       <input type="text"  name="Origin_Country" value="<?php echo e(old('Origin_Country')); ?>" class="form-control">
                                    </div>
                                     
                                                              <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.SearchCode1')); ?>  </label>
                                       <input type="text"  name="SearchCode1" value="<?php echo e(old('SearchCode1')); ?>" class="form-control">
                                    </div>
                                     
                                                              <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.SearchCode2')); ?>  </label>
                                       <input type="text"  name="SearchCode2" value="<?php echo e(old('SearchCode2')); ?>" class="form-control">
                                    </div>
                                     
                                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Space')); ?>  </label>
                                       <input type="text"  name="Space" value="<?php echo e(old('Space')); ?>" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Storage')); ?>  </label>
                                       <input type="text"  name="Storage" value="<?php echo e(old('Storage')); ?>" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Processor')); ?>  </label>
                                       <input type="text"  name="Processor" value="<?php echo e(old('Processor')); ?>" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Camera')); ?>  </label>
                                       <input type="text"  name="Camera" value="<?php echo e(old('Camera')); ?>" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Screen')); ?>  </label>
                                       <input type="text"  name="Screen" value="<?php echo e(old('Screen')); ?>" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.OS')); ?>  </label>
                                       <input type="text"  name="OS" value="<?php echo e(old('OS')); ?>" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Battery')); ?>  </label>
                                       <input type="text"  name="Battery" value="<?php echo e(old('Battery')); ?>" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Warranty')); ?>  </label>
                                       <input type="text"  name="Warranty" value="<?php echo e(old('Warranty')); ?>" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Color')); ?>  </label>
                                       <input type="text"  name="Color" value="<?php echo e(old('Color')); ?>" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Category')); ?>  </label>
                                       <input type="text"  name="Category" value="<?php echo e(old('Category')); ?>" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Model')); ?>  </label>
                                       <input type="text"  name="Model" value="<?php echo e(old('Model')); ?>" class="form-control">
                                    </div>                          
                                     
                                     
                                     <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Arrange')); ?>  </label>
                                       <input type="number"  name="Arrange" value="<?php echo e(old('Arrange')); ?>" class="form-control">
                                    </div>     
                                     
                                     <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Calories')); ?>  </label>
                                       <input type="text"  name="Calories" value="<?php echo e(old('Calories')); ?>" class="form-control">
                                    </div>
                                     
           
                                    <div class="form-group col-md-2">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Validity')); ?> </label>
                       <select class="select2 form-control w-100" name="Validity" id="Validity" onchange="ShowD()">
           <option value="0" <?php if($DefSHOW->Validity_Product  == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.NO')); ?></option>
           <option value="1" <?php if($DefSHOW->Validity_Product  == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?></option>
                                       </select>
                                    </div>
                                     
                                              <div class="form-group col-md-2" id="Days_Notify" style="display: none">
                                       <label class="form-label" for="simpleinput">
                                       <?php echo e(trans('admin.Days_Notify')); ?>

                                       </label>
                                       <input type="number"  name="Days_Notify" value="<?php echo e(old('Days_Notify')); ?>" class="form-control">
                                    </div>
                                     
                                     
                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Code_Typeee')); ?> </label>
                       <select class="select2 form-control w-100" name="Code_Type">
           <option value=""><?php echo e(trans('admin.Code_Typeee')); ?></option>
           <option value="GS1" <?php if($Def->CodeType == 'GS1'): ?> selected <?php endif; ?>>GS1</option>
           <option value="EGS" <?php if($Def->CodeType == 'EGS'): ?> selected <?php endif; ?>>EGS</option>
                                       </select>
                                    </div>
                                     
                                                   <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.World_Code')); ?> </label>
                                       <input type="text"  name="World_Code" value="<?php echo e(old('World_Code')); ?>" class="form-control">
                                    </div>

                           
                                    <div class="form-group col-md-2">
                                       <label class="form-label" for=""><?php echo e(trans('admin.Show_Other_Store')); ?>  </label>
                                       <select class="select2 form-control w-100" name="Show_Other_Store">
                                          <option value="0"><?php echo e(trans('admin.NO')); ?></option>
                                          <option value="1"><?php echo e(trans('admin.Yes')); ?></option>
                             
                                       </select>
                                    </div>                    
                                     
                                     <div class="form-group col-md-2">
                                       <label class="form-label" for=""><?php echo e(trans('admin.Store_Show')); ?>  </label>
                                       <select class="select2 form-control w-100" name="Store_Show" id="Store_Show" onchange="Show()">
                                          <option value="0"><?php echo e(trans('admin.NO')); ?></option>
                                          <option value="1"><?php echo e(trans('admin.Storee')); ?></option>
                                          <option value="2"><?php echo e(trans('admin.Price_List')); ?></option>
                                          <option value="3"><?php echo e(trans('admin.Both')); ?></option>
                                       </select>
                                    </div>
                                    <div class="form-group col-md-2" id="StoreType" style="display: none">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Store_Type')); ?> </label>
                                       <select class="select2 form-control w-100" name="Store_Type">
                                          <option value=""><?php echo e(trans('admin.Store_Type')); ?></option>
                                          <option value="0"><?php echo e(trans('admin.Recently')); ?></option>
                                          <option value="1"><?php echo e(trans('admin.Offer')); ?></option>
                                          <option value="2"><?php echo e(trans('admin.Most_Salling')); ?></option>
                                       </select>
                                    </div>
                                    <div class="form-group col-md-3 mb-3">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Sub_Images')); ?></label>
                                       <input type="file" name="SubImage[]" multiple>
                                         
                                    
                                    </div>
                                                <div class="form-group col-md-3" style="display: none">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Ar_Desc')); ?>  </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="">
                                       <?php echo e(old('Ar_Desc')); ?>

                                       </textarea>  
                                    </div>
                                    <div class="form-group col-md-3">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Ar_Desc')); ?>  </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="Ar_Desc">
                                       <?php echo e(old('Ar_Desc')); ?>

                                       </textarea>  
                                    </div>
                                    <div class="form-group col-md-3">
                                       <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.En_Desc')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="En_Desc">
                                       <?php echo e(old('En_Desc')); ?>

                                       </textarea>
                                    </div>
                                           <div class="form-group col-md-3">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Arabic_Brief_Desc')); ?>  </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="Arabic_Brief_Desc">
                                       <?php echo e(old('Arabic_Brief_Desc')); ?>

                                       </textarea>  
                                    </div>
                                    <div class="form-group col-md-3">
                                       <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.English_Brief_Desc')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="English_Brief_Desc">
                                       <?php echo e(old('English_Brief_Desc')); ?>

                                       </textarea>
                                    </div>
                                     
                                     
                                     


                                     
                                    <div class="form-group col-md-3">
                                       <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Ar_Spec')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="Ar_Spec">
                                       <?php echo e(old('Ar_Spec')); ?>

                                       </textarea>
                                    </div>
                                    <div class="form-group col-md-3">
                                       <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.En_Spec')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="En_Spec">
                                       <?php echo e(old('En_Spec')); ?>

                                       </textarea>
                                    </div>
                                  
                                    
                                 </div>
                              </div>
                                <div class="tab-pane fade" id="tab_borders_icons-3" role="tabpanel">
                                    
                                 <div class="form-row">
                         
                                    <div class="form-group col-md-8">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Product')); ?> </label>
                       <select class="select2 form-control w-100"  id="AdditionProduct" onchange="AdditionPlus()">
                               <option value=""><?php echo e(trans('admin.Product')); ?></option>
   
                           <?php $__currentLoopData = $AdditionsProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $addd): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                           
                                  <option value="<?php echo e($addd->id); ?>"><?php echo e(app()->getLocale() == 'ar' ?$addd->P_Ar_Name :$addd->P_En_Name); ?></option>
                           <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                       </select>
                                    </div>
                                     
                      
                                     
                                            <div class="form-group col-md-4">
                            <button type="button" onclick="InsertAddition()" class="btn btn-default" id="addAdditionP" style="display: none"><i class="fal fa-plus"></i></button>
                                     </div>
                                     
                                     
           <!-- datatable start -->
                                                                <div id="mobile-overflow">
                                                                <table id=""
                                                                    class="table table-bordered table-hover table-striped w-100 mobile-width">
                                                                    <thead>
                                                                        <tr>
                                                                        <th><?php echo e(trans('admin.Product')); ?></th>
                                                                        <th><?php echo e(trans('admin.Actions')); ?></th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody id="dataAddition">
                                                                     
                                                                    </tbody>
                                                                  
                                                                </table>
                                                                </div>
                                                                <!-- datatable end -->
                                                           
                                   
                                    
                                 </div>
                              </div>
                               
                           </div>
                           <div class="buttons mt-3">
                              <button type="submit" id="SUBMIT" style="display: none" class="btn btn-primary"><i class="fal fa-save"></i> <?php echo e(trans('admin.Save')); ?> </button>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </form>
</main>
<input type="hidden" id="HIDEEE" value="0">


<?php if(app()->getLocale() == 'ar' ): ?> 
<input type="hidden" id="LANG" value="ar">
<?php else: ?>
<input type="hidden" id="LANG" value="en">
<?php endif; ?>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
<link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
<link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">
<link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/dropzone/dropzone.css')); ?>">
<style>
   .dt-buttons{
   display: none;
   }
   .dataTables_filter{
   display: none;
   }
</style>
<script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
<script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
<script src="<?php echo e(asset('Admin/js/formplugins/summernote/summernote.js')); ?>"></script>
<script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
<script src="<?php echo e(asset('Admin/js/formplugins/dropzone/dropzone.js')); ?>"></script>
<!--- Plus -->
<script>
   function PLUS(){
       
       
        $.fn.rowCount = function() {
       return $('tr', $(this).find('tbody')).length;
     };
     
    var rowctr = $('#samble').rowCount(); 
      var  P_Type =$('#P_Type').val(); 
     
       if(P_Type == 'Assembly' || P_Type == 'Serial' || P_Type == 'Variable_Aggregate'){
           if(rowctr == 0){
               
                           var Unit =$('#unit').val();
       var Rate =$('#rate').val();
       var Code =$('#code').val();
       var Price =$('#price1').val();
       
       if(Unit == ''  || Rate == ''  ||  Code == '' || Price == ''){
           
          document.getElementById("add-data").style.display = "none";           
       }
   
       
         if(Unit != ''  && Rate != ''  &&  Code != '' && Price != ''){
           
          document.getElementById("add-data").style.display = "block";           
       }  
             
             
               
           }else{
               
       document.getElementById("add-data").style.display = "none";    
               
           }
           
           
           
       }else{
       
       var Unit =$('#unit').val();
       var Rate =$('#rate').val();
       var Code =$('#code').val();
       var Price =$('#price1').val();
       
       if(Unit == ''  || Rate == ''  ||  Code == '' || Price == ''){
           
          document.getElementById("add-data").style.display = "none";           
       }
   
       
         if(Unit != ''  && Rate != ''  &&  Code != '' && Price != ''){
           
          document.getElementById("add-data").style.display = "block";           
       }
       }
   }
</script>
<!-- Unit Name -->
<script>
   $(document).ready(function() {
   
                  $('#unit').on('change', function(){
                      var countryId = $(this).val();
                      if(countryId) {
                          $.ajax({
                              url: 'UnitNameFilter/'+countryId,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                                  $.each(data, function(key, value){
   
        
                        $('#UnitID').val(key); 
                    $('#UnitName').val(value); 
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {
   
                          $('select[name="state"]').empty();
                      }
   
                  });
   
              });
    
       $(document).ready(function() {
                    var countryId = $("#unit").val();
                      if(countryId) {
                          $.ajax({
                              url: 'UnitNameFilter/'+countryId,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                                  $.each(data, function(key, value){
   
        
                        $('#UnitID').val(key); 
                    $('#UnitName').val(value); 
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {
   
                          $('select[name="state"]').empty();
                      }
           });
   

   function  UnitNameCode(x){
    
    var countryId = $('#UnitAssem'+x).val();
    var Pro = $('#Product'+x).val();
                      if(countryId) {
                          $.ajax({
                              url: 'UnitNameCodeFilterr/'+countryId+'/'+Pro,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                                  $.each(data, function(key, value){
                                      
                        $('#CodeAssem'+x).val(data.code); 
                    $('#UnitNameAssem'+x).val(data.name); 
                    $('#Price'+x).val(data.price); 
                                      
   
                                  });
                                  
                                  
        var Qty = $("#Qty"+x).val();
   var Price = $("#Price"+x).val();
   
    var result = parseFloat(Qty) *  parseFloat(Price) ;  
       $("#Total"+x).val(result);
       
        var Total = $("#Total"+x).val();
        var UnitID = $("#UnitAssem"+x).val();
       
       
       if(Qty == ''  || Price == ''  ||  Total == '' || UnitID == ''){
         
        document.getElementById("AddBtnPur"+x).style.display = "none";           
     }
   
     
       if(Qty != ''  && Price != ''  &&  Total != '' && UnitID != ''){
         
        document.getElementById("AddBtnPur"+x).style.display = "block";           
     }  
            
         
                                  
                                  
                                  
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {
   
                          $('select[name="state"]').empty();
                      }
   
   }   
    
</script>  
<!-- Store Show -->
<script>
   function Show(){
     var  Store_Show =$('#Store_Show').val(); 
       
       if(Store_Show == 1){
           
           document.getElementById("StoreType").style.display = "block";   
       }else{
          document.getElementById("StoreType").style.display = "none";    
           
       }
       
   }
</script>
<!-- Days Notify Show -->
<script>
   function ShowD(){
     var  Validity =$('#Validity').val(); 
       
       if(Validity == 1){
           
           document.getElementById("Days_Notify").style.display = "block";   
       }else{
          document.getElementById("Days_Notify").style.display = "none";    
           
       }
       
   }
       $(document).ready(function() {
        
               var  Validity =$('#Validity').val(); 
       
       if(Validity == 1){
           
           document.getElementById("Days_Notify").style.display = "block";   
       }else{
          document.getElementById("Days_Notify").style.display = "none";    
           
       }
           
           
       });
</script>
<!-- Assembly and Virables Show -->
<script>
   function Assembly(){
     var  P_Type =$('#P_Type').val(); 
       
       $("#samble").find("tr:gt(0)").remove();
           document.getElementById("SUBMIT").style.display = "none"; 
       if(P_Type == 'Assembly'){
           
           document.getElementById("HideAssembly").style.display = "block";  
            document.getElementById("HideViraOne").style.display = "none";  
            document.getElementById("HideViraTwo").style.display = "none";  
       document.getElementById("EndDate").style.display = "none"; 
       document.getElementById("HideVariable_Aggregate").style.display = "none"; 
                        document.getElementById('unit').disabled = false;
            document.getElementById('rate').disabled = false;
            $('#rate').val(1);
            document.getElementById('price1').disabled = false;
              $('#price1').val('');
            document.getElementById('price2').disabled = false;
              $('#price2').val('');
            document.getElementById('price3').disabled = false;
              $('#price3').val('');
            document.getElementById("add-data").style.display = "none";  
   
       }else if(P_Type == 'Single_Variable'){
           
           document.getElementById("HideAssembly").style.display = "none";  
            document.getElementById("HideViraOne").style.display = "block";  
            document.getElementById("HideViraTwo").style.display = "none";   
                document.getElementById("EndDate").style.display = "none"; 
                document.getElementById("HideVariable_Aggregate").style.display = "none"; 
                        document.getElementById('unit').disabled = false;
            document.getElementById('rate').disabled = false;
            $('#rate').val(1);
            document.getElementById('price1').disabled = false;
              $('#price1').val('');
            document.getElementById('price2').disabled = false;
              $('#price2').val('');
            document.getElementById('price3').disabled = false;
              $('#price3').val('');
            document.getElementById("add-data").style.display = "none";  

           
       }else if(P_Type == 'Duble_Variable'){
              document.getElementById("HideAssembly").style.display = "none";  
            document.getElementById("HideViraOne").style.display = "none";  
            document.getElementById("HideViraTwo").style.display = "block";  
                document.getElementById("EndDate").style.display = "none"; 
                document.getElementById("HideVariable_Aggregate").style.display = "none"; 
                        document.getElementById('unit').disabled = false;
            document.getElementById('rate').disabled = false;
            $('#rate').val(1);
            document.getElementById('price1').disabled = false;
              $('#price1').val('');
            document.getElementById('price2').disabled = false;
              $('#price2').val('');
            document.getElementById('price3').disabled = false;
              $('#price3').val('');
            document.getElementById("add-data").style.display = "none";  

           
       }else if(P_Type == 'Subscribe'){
              document.getElementById("HideAssembly").style.display = "none";  
            document.getElementById("HideViraOne").style.display = "none";  
            document.getElementById("HideViraTwo").style.display = "none";  
            document.getElementById("HideVariable_Aggregate").style.display = "none";  
            document.getElementById("EndDate").style.display = "block";  
                        document.getElementById('unit').disabled = false;
            document.getElementById('rate').disabled = false;
            $('#rate').val(1);
            document.getElementById('price1').disabled = false;
              $('#price1').val('');
            document.getElementById('price2').disabled = false;
              $('#price2').val('');
            document.getElementById('price3').disabled = false;
              $('#price3').val('');
            document.getElementById("add-data").style.display = "none";  
           
        }else if(P_Type == 'Variable_Aggregate'){
              document.getElementById("HideAssembly").style.display = "none";  
            document.getElementById("HideViraOne").style.display = "none";  
            document.getElementById("HideViraTwo").style.display = "none";  
            document.getElementById("HideVariable_Aggregate").style.display = "block";  
            document.getElementById("EndDate").style.display = "none";  
         
            
    
            document.getElementById('unit').disabled = true;
            document.getElementById('rate').disabled = true;
            $('#rate').val(1);
            document.getElementById('price1').disabled = true;
              $('#price1').val(1);
            document.getElementById('price2').disabled = true;
              $('#price2').val(1);
            document.getElementById('price3').disabled = true;
              $('#price3').val(1);
            document.getElementById("add-data").style.display = "block";  

  $.ajax({
                   url: 'VASelectFilterr',
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#PriceVASelect').empty();
   
                       $.each(data, function(key, value){
   
             $('#PriceVASelect').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
            
                       
                                   var VA=$('#PriceVASelect').val();
                          $.ajax({
                   url: 'VASubSelectFilterr',
                   type:"GET",
                   data:{
                       VA:VA
                   },
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#PriceSubVASelect').empty();
   
                       $.each(data, function(key, value){
   
             $('#PriceSubVASelect').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
            
                       
                               
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });       
                               
          
                       
                       
        
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });     



           
       }else{
           
              document.getElementById("HideAssembly").style.display = "none";  
            document.getElementById("HideViraOne").style.display = "none";  
            document.getElementById("HideViraTwo").style.display = "none";  
             document.getElementById("EndDate").style.display = "none";  
             document.getElementById("HideVariable_Aggregate").style.display = "none";  
           
           
                     document.getElementById('unit').disabled = false;
            document.getElementById('rate').disabled = false;
            $('#rate').val(1);
            document.getElementById('price1').disabled = false;
              $('#price1').val('');
            document.getElementById('price2').disabled = false;
              $('#price2').val('');
            document.getElementById('price3').disabled = false;
              $('#price3').val('');
            document.getElementById("add-data").style.display = "none";  

           
       }
       
   
     
       } 
      $(document).ready(function() {
     
               var  P_Type =$('#P_Type').val(); 
       
       $("#samble").find("tr:gt(0)").remove();
           document.getElementById("SUBMIT").style.display = "none"; 
       if(P_Type == 'Assembly'){
           
           document.getElementById("HideAssembly").style.display = "block";  
            document.getElementById("HideViraOne").style.display = "none";  
            document.getElementById("HideViraTwo").style.display = "none";  
       document.getElementById("EndDate").style.display = "none"; 
       document.getElementById("HideVariable_Aggregate").style.display = "none"; 
                        document.getElementById('unit').disabled = false;
            document.getElementById('rate').disabled = false;
            $('#rate').val(1);
            document.getElementById('price1').disabled = false;
              $('#price1').val('');
            document.getElementById('price2').disabled = false;
              $('#price2').val('');
            document.getElementById('price3').disabled = false;
              $('#price3').val('');
            document.getElementById("add-data").style.display = "none";  
   
       }else if(P_Type == 'Single_Variable'){
           
           document.getElementById("HideAssembly").style.display = "none";  
            document.getElementById("HideViraOne").style.display = "block";  
            document.getElementById("HideViraTwo").style.display = "none";   
                document.getElementById("EndDate").style.display = "none"; 
                document.getElementById("HideVariable_Aggregate").style.display = "none"; 
                        document.getElementById('unit').disabled = false;
            document.getElementById('rate').disabled = false;
            $('#rate').val(1);
            document.getElementById('price1').disabled = false;
              $('#price1').val('');
            document.getElementById('price2').disabled = false;
              $('#price2').val('');
            document.getElementById('price3').disabled = false;
              $('#price3').val('');
            document.getElementById("add-data").style.display = "none";  

           
       }else if(P_Type == 'Duble_Variable'){
              document.getElementById("HideAssembly").style.display = "none";  
            document.getElementById("HideViraOne").style.display = "none";  
            document.getElementById("HideViraTwo").style.display = "block";  
                document.getElementById("EndDate").style.display = "none"; 
                document.getElementById("HideVariable_Aggregate").style.display = "none"; 
                        document.getElementById('unit').disabled = false;
            document.getElementById('rate').disabled = false;
            $('#rate').val(1);
            document.getElementById('price1').disabled = false;
              $('#price1').val('');
            document.getElementById('price2').disabled = false;
              $('#price2').val('');
            document.getElementById('price3').disabled = false;
              $('#price3').val('');
            document.getElementById("add-data").style.display = "none";  

           
       }else if(P_Type == 'Subscribe'){
              document.getElementById("HideAssembly").style.display = "none";  
            document.getElementById("HideViraOne").style.display = "none";  
            document.getElementById("HideViraTwo").style.display = "none";  
            document.getElementById("HideVariable_Aggregate").style.display = "none";  
            document.getElementById("EndDate").style.display = "block";  
                        document.getElementById('unit').disabled = false;
            document.getElementById('rate').disabled = false;
            $('#rate').val(1);
            document.getElementById('price1').disabled = false;
              $('#price1').val('');
            document.getElementById('price2').disabled = false;
              $('#price2').val('');
            document.getElementById('price3').disabled = false;
              $('#price3').val('');
            document.getElementById("add-data").style.display = "none";  
        }else if(P_Type == 'Variable_Aggregate'){
              document.getElementById("HideAssembly").style.display = "none";  
            document.getElementById("HideViraOne").style.display = "none";  
            document.getElementById("HideViraTwo").style.display = "none";  
            document.getElementById("HideVariable_Aggregate").style.display = "block";  
            document.getElementById("EndDate").style.display = "none";  
            
            
             
            document.getElementById('unit').disabled = true;
            document.getElementById('rate').disabled = true;
            $('#rate').val(1);
            document.getElementById('price1').disabled = true;
              $('#price1').val(1);
            document.getElementById('price2').disabled = true;
              $('#price2').val(1);
            document.getElementById('price3').disabled = true;
              $('#price3').val(1);
            document.getElementById("add-data").style.display = "block";  

                   $.ajax({
                   url: 'VASelectFilterr',
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#PriceVASelect').empty();
   
                       $.each(data, function(key, value){
   
             $('#PriceVASelect').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
            
                       
                                   var VA=$('#PriceVASelect').val();
                          $.ajax({
                   url: 'VASubSelectFilterr',
                   type:"GET",
                   data:{
                       VA:VA
                   },
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#PriceSubVASelect').empty();
   
                       $.each(data, function(key, value){
   
             $('#PriceSubVASelect').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
            
                       
                               
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });       
                               
          
                       
                       
        
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });     

           
       }else{
           
              document.getElementById("HideAssembly").style.display = "none";  
            document.getElementById("HideViraOne").style.display = "none";  
            document.getElementById("HideViraTwo").style.display = "none";  
             document.getElementById("EndDate").style.display = "none";  
             document.getElementById("HideVariable_Aggregate").style.display = "none";  
                        document.getElementById('unit').disabled = false;
            document.getElementById('rate').disabled = false;
            $('#rate').val(1);
            document.getElementById('price1').disabled = false;
              $('#price1').val('');
            document.getElementById('price2').disabled = false;
              $('#price2').val('');
            document.getElementById('price3').disabled = false;
              $('#price3').val('');
            document.getElementById("add-data").style.display = "none";  

           
       }
       
   
       
          
      });
</script>
<!-- Genrte Code  -->
<script>
   function Genrte(){
         
    $( "#code" ).val(Math.floor(Math.random() * 1000000));
   
   }
    
       $(document).ready(function(){
         $( "#code" ).val(Math.floor(Math.random() * 1000000));    
           
       });
   
</script>
<!--  Filter Assembly -->
<script>
   $(document).ready(function(){
   
    fetch_customer_data();
   
    function fetch_customer_data(search = '')
    {
     $.ajax({
      url:'AssemblyFilter',
      method:'GET',
      data:{search:search},
      dataType:'json',
      success:function(data)
      {
       $('#data').html(data.table_data);
      }
     })
    }
    
   $(document).on('keyup', '#search', function(){
     var search = $(this).val();     
     fetch_customer_data(search);
    });
   
       
   });
</script>
<!-- Add Assembly -->
<script>
   function Fun(r) { 
       
             var P_Ar_Name = $("#P_Ar_Name"+r).val();
             var P_En_Name= $("#P_En_Name"+r).val();
             var Product = $("#Product"+r).val();
             var UnitID = $("#UnitAssem"+r).val();
             var UnitName = $("#UnitNameAssem"+r).val();
             var Qty = $("#Qty"+r).val();
             var Barcode = $("#CodeAssem"+r).val();
             var Price = $("#Price"+r).val();
             var Total = $("#Total"+r).val();
             var LANG = $("#LANG").val();
   
             document.getElementById("AddBtnPur"+r).style.display = "none";
             document.getElementById("Row"+r).style.display = "none";
       
      
          if(LANG == 'ar' ){ 
          var Nemo = P_Ar_Name ;
          }else{
             var Nemo = P_En_Name ;   
          }
       
             var markup = "<tr><td><input type='hidden' name='P_Ar_NameAssem[]' value='"+P_Ar_Name+"'><input type='hidden' name='P_En_NameAssem[]' value='"+P_En_Name+"'>" + Nemo + "</td><td><input type='hidden' name='P_CodeAssem[]' value='"+Barcode+"'>" + Barcode + "</td><td><input type='hidden' name='UnitAssem[]' value='"+UnitID+"'>" + UnitName + "</td><td><input type='hidden' name='QtyAssem[]' value='"+Qty+"'>" + Qty + "</td><td><input type='hidden' name='PriceAssem[]' value='"+Price+"'>" + Price + "</td><td><input type='hidden' name='TotalAssem[]' class='toot' value='"+Total+"'>" + Total + "</td><td>  <button id='DelAssem' type='button' class='btn btn-default'><i class='fal fa-trash'></i></button><input id='Product_IDInp"+r+"' type='hidden' name='ProductAssem[]' value='"+Product+"'></td></tr>";
   
            var  Product_IDInp =$("#Product_IDInp"+r).val();
       
         if(Product != Product_IDInp){
             $("#data-dt").append(markup);
         }
       
       
          var sumQ = 0;        
   $('.toot').each(function(){
   sumQ += parseFloat($(this).val());
   });   
          
              $("#AssemblyTotal").val(sumQ);
      
        $('#data-dt').on('click', '#DelAssem', function(e){
                $(this).closest('tr').remove(); 
            
                      var sumQ = 0;        
   $('.toot').each(function(){
   sumQ += parseFloat($(this).val());
   });   
          
              $("#AssemblyTotal").val(sumQ);
                    })  
       
       
     }    
</script> 
<!-- Total Assembly -->
<script>
   function AssTotal(r) { 
   var Qty = $("#Qty"+r).val();
   var Price = $("#Price"+r).val();
   
    var result = parseFloat(Qty) *  parseFloat(Price) ;  
       $("#Total"+r).val(result);
       
        var Total = $("#Total"+r).val();
        var UnitID = $("#UnitAssem"+r).val();
       
       
       if(Qty == ''  || Price == ''  ||  Total == '' || UnitID == ''){
         
        document.getElementById("AddBtnPur"+r).style.display = "none";           
     }
   
     
       if(Qty != ''  && Price != ''  &&  Total != '' && UnitID != ''){
         
        document.getElementById("AddBtnPur"+r).style.display = "block";           
     }  
            
       
       
       
       
   }
</script>
<!-- Filter Virable One -->
<script>
   $(document).ready(function(){
   
    fetch_customer_data();
   
    function fetch_customer_data(search = '')
    {
     $.ajax({
      url:'VOneFilter',
      method:'GET',
      data:{search:search},
      dataType:'json',
      success:function(data)
      {
       $('.ViraOne').html(data.table_data);
      }
     })
    }
    
   $(document).on('change', '#VOne', function(){
     var search = $(this).val();  
     fetch_customer_data(search);
    });
   
       
   });
</script>
<!-- Filter Virable Two -->
<script>
   $(document).ready(function(){
    
    fetch_customer_data();
   
    function fetch_customer_data(search = '',searchT='')
    {
     $.ajax({
      url:'VTwoFilter',
      method:'GET',
      data:{search:search,searchT:searchT},
      dataType:'json',
      success:function(data)
      {
       $('#variables').html(data.table_data);
      }
     })
    }
    
   $(document).on('change', '#VTwo', function(){
     var search = $(this).val();  
     var searchT = $("#VTwoo").val();  
      
            if(search != searchT){  
        document.getElementById("AlertVira").style.display = "none";       
        document.getElementById("TabV2").style.display = "block";   
         fetch_customer_data(search,searchT);     
    }else{
        
        
       document.getElementById("AlertVira").style.display = "block";   
       document.getElementById("TabV2").style.display = "none";   
        
    }
      
   
    });
       
   $(document).on('change', '#VTwoo', function(){
     var searchT = $(this).val();  
     var search = $("#VTwo").val(); 
                  if(search != searchT){  
        document.getElementById("AlertVira").style.display = "none";       
        document.getElementById("TabV2").style.display = "block";   
         fetch_customer_data(search,searchT);     
    }else{
        
        
       document.getElementById("AlertVira").style.display = "block";   
       document.getElementById("TabV2").style.display = "none";   
        
    }     
             
    });
        
   
       
   });
</script>
<script>
   /* demo scripts for change table color */
   /* change background */
   
   
   $(document).ready(function () {
   
       $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
       $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
           var title = $(this).text();
           $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search ' + title + '" />');
   
           $('input', this).on('keyup change', function () {
               if (table.column(i).search() !== this.value) {
                   table
                       .column(i)
                       .search(this.value)
                       .draw();
               }
           });
       });
   
       var table = $('#dt-basic-example').dataTable(
           {
               responsive: true,
               orderCellsTop: true,
               fixedHeader: true,
               lengthChange: false,
   
               dom: "<'row mb-3'<'col-sm-12 col-md-6 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-6 d-flex align-items-center justify-content-end'B>>" +
                   "<'row'<'col-sm-12'tr>>" +
                   "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
               buttons: [
                   {
                       extend: 'colvis',
                       text: 'Column Visibility',
                       titleAttr: 'Col visibility',
                       className: 'btn-outline-default'
                   },
                   {
                       extend: 'pdfHtml5',
                       text: 'PDF',
                       titleAttr: 'Generate PDF',
                       className: 'btn-outline-danger btn-sm mr-1'
                   },
                   {
                       extend: 'excelHtml5',
                       text: 'Excel',
                       titleAttr: 'Generate Excel',
                       className: 'btn-outline-success btn-sm mr-1'
                   },
                   {
                       extend: 'csvHtml5',
                       text: 'CSV',
                       titleAttr: 'Generate CSV',
                       className: 'btn-outline-primary btn-sm mr-1'
                   },
                   {
                       extend: 'copyHtml5',
                       text: 'Copy',
                       titleAttr: 'Copy to clipboard',
                       className: 'btn-outline-primary btn-sm mr-1'
                   },
                   {
                       extend: 'print',
                       text: 'Print',
                       titleAttr: 'Print Table',
                       className: 'btn-outline-primary btn-sm'
                   }
               ],
           });
   
       $('.js-thead-colors a').on('click', function () {
           var theadColor = $(this).attr("data-bg");
           console.log(theadColor);
           $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
       });
   
       $('.js-tbody-colors a').on('click', function () {
           var theadColor = $(this).attr("data-bg");
           console.log(theadColor);
           $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
       });
   
   });
   
</script>
<script>
   var autoSave = $('#autoSave');
   var interval;
   var timer = function()
   {
       interval = setInterval(function()
       {
           //start slide...
           if (autoSave.prop('checked'))
               saveToLocal();
   
           clearInterval(interval);
       }, 3000);
   };
   
   //save
   var saveToLocal = function()
   {
       localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
       console.log("saved");
   }
   
   //delete 
   var removeFromLocal = function()
   {
       localStorage.removeItem("summernoteData");
       $('#saveToLocal').summernote('reset');
   }
   
   $(document).ready(function()
   {
       //init default
       $('.js-summernote').summernote(
       {
           height: 200,
           tabsize: 2,
           placeholder: "Type here...",
           dialogsFade: true,
           toolbar: [
               ['style', ['style']],
               ['font', ['strikethrough', 'superscript', 'subscript']],
               ['font', ['bold', 'italic', 'underline', 'clear']],
               ['fontsize', ['fontsize']],
               ['fontname', ['fontname']],
               ['color', ['color']],
               ['para', ['ul', 'ol', 'paragraph']],
               ['height', ['height']]
               ['table', ['table']],
               ['insert', ['link', 'picture', 'video']],
               ['view', ['fullscreen', 'codeview', 'help']]
           ],
           callbacks:
           {
               //restore from localStorage
               onInit: function(e)
               {
                   $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
               },
               onChange: function(contents, $editable)
               {
                   clearInterval(interval);
                   timer();
               }
           }
       });
   
       //load emojis
       $.ajax(
       {
           url: 'https://api.github.com/emojis',
           async: false
       }).then(function(data)
       {
           window.emojis = Object.keys(data);
           window.emojiUrls = data;
       });
   
       //init emoji example
       $(".js-hint2emoji").summernote(
       {
           height: 100,
           toolbar: false,
           placeholder: 'type starting with : and any alphabet',
           hint:
           {
               match: /:([\-+\w]+)$/,
               search: function(keyword, callback)
               {
                   callback($.grep(emojis, function(item)
                   {
                       return item.indexOf(keyword) === 0;
                   }));
               },
               template: function(item)
               {
                   var content = emojiUrls[item];
                   return '<img src="' + content + '" width="20" /> :' + item + ':';
               },
               content: function(item)
               {
                   var url = emojiUrls[item];
                   if (url)
                   {
                       return $('<img />').attr('src', url).css('width', 20)[0];
                   }
                   return '';
               }
           }
       });
   
       //init mentions example
       $(".js-hint2mention").summernote(
       {
           height: 100,
           toolbar: false,
           placeholder: "type starting with @",
           hint:
           {
               mentions: ['jayden', 'sam', 'alvin', 'david'],
               match: /\B@(\w*)$/,
               search: function(keyword, callback)
               {
                   callback($.grep(this.mentions, function(item)
                   {
                       return item.indexOf(keyword) == 0;
                   }));
               },
               content: function(item)
               {
                   return '@' + item;
               }
           }
       });
   
   });
   
</script>
<script>
   $(document).ready(function()
   {
       $(function()
       {
           $('.select2').select2();
   
           $(".select2-placeholder-multiple").select2(
           {
               placeholder: "Select State"
           });
           $(".js-hide-search").select2(
           {
               minimumResultsForSearch: 1 / 0
           });
           $(".js-max-length").select2(
           {
               maximumSelectionLength: 2,
               placeholder: "Select maximum 2 items"
           });
           $(".select2-placeholder").select2(
           {
               placeholder: "Select a state",
               allowClear: true
           });
   
           $(".js-select2-icons").select2(
           {
               minimumResultsForSearch: 1 / 0,
               templateResult: icon,
               templateSelection: icon,
               escapeMarkup: function(elm)
               {
                   return elm
               }
           });
   
           function icon(elm)
           {
               elm.element;
               return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
           }
   
           $(".js-data-example-ajax").select2(
           {
               ajax:
               {
                   url: "https://api.github.com/search/repositories",
                   dataType: 'json',
                   delay: 250,
                   data: function(params)
                   {
                       return {
                           q: params.term, // search term
                           page: params.page
                       };
                   },
                   processResults: function(data, params)
                   {
                       // parse the results into the format expected by Select2
                       // since we are using custom formatting functions we do not need to
                       // alter the remote JSON data, except to indicate that infinite
                       // scrolling can be used
                       params.page = params.page || 1;
   
                       return {
                           results: data.items,
                           pagination:
                           {
                               more: (params.page * 30) < data.total_count
                           }
                       };
                   },
                   cache: true
               },
               placeholder: 'Search for a repository',
               escapeMarkup: function(markup)
               {
                   return markup;
               }, // let our custom formatter work
               minimumInputLength: 1,
               templateResult: formatRepo,
               templateSelection: formatRepoSelection
           });
   
           function formatRepo(repo)
           {
               if (repo.loading)
               {
                   return repo.text;
               }
   
               var markup = "<div class='select2-result-repository clearfix d-flex'>" +
                   "<div class='select2-result-repository__avatar mr-2'><img src='" + repo.owner.avatar_url + "' class='width-2 height-2 mt-1 rounded' /></div>" +
                   "<div class='select2-result-repository__meta'>" +
                   "<div class='select2-result-repository__title fs-lg fw-500'>" + repo.full_name + "</div>";
   
               if (repo.description)
               {
                   markup += "<div class='select2-result-repository__description fs-xs opacity-80 mb-1'>" + repo.description + "</div>";
               }
   
               markup += "<div class='select2-result-repository__statistics d-flex fs-sm'>" +
                   "<div class='select2-result-repository__forks mr-2'><i class='fal fa-lightbulb'></i> " + repo.forks_count + " Forks</div>" +
                   "<div class='select2-result-repository__stargazers mr-2'><i class='fal fa-star'></i> " + repo.stargazers_count + " Stars</div>" +
                   "<div class='select2-result-repository__watchers mr-2'><i class='fal fa-eye'></i> " + repo.watchers_count + " Watchers</div>" +
                   "</div>" +
                   "</div></div>";
   
               return markup;
           }
   
           function formatRepoSelection(repo)
           {
               return repo.full_name || repo.text;
           }
       });
   });
   
</script>
<script>
   function InsertData(){
   var UnitName = document.getElementById('UnitName').value;
   var UnitID = document.getElementById('UnitID').value;
   var Rate = document.getElementById('rate').value;
   var Code = document.getElementById('code').value;
   var Price1 = document.getElementById('price1').value;
   var Price2 = document.getElementById('price2').value;
   var Price3 = document.getElementById('price3').value;
   var HIDEEE=0;   
   var table =  ` <tr> 
                           <td>
                     ${UnitName}
   <input type="hidden" name="Rate[]" value="${Rate}">
   <input type="hidden" name="Barcode[]" value="${Code}">
   <input type="hidden" name="Price[]" value="${Price1}">
   <input type="hidden" name="Price_Two[]" value="${Price2}">
   <input type="hidden" name="Price_Three[]" value="${Price3}">
   <input type="hidden" id="U${UnitID}" name="Unit[]" value="${UnitID}">
                       </td>
                           <td>${Rate}</td>
                           <td>${Code}</td>
                           <td>${Price1}</td>
                           <td>${Price2}</td>
                           <td>${Price3}</td>
                           <td>
   
           <input type="radio" onclick="DEFAULT(${UnitID})"  name="DefaultUnit"  required>        
           <input type="hidden" class="default" id="def${UnitID}" name="Def[]" value="0">
                          </td>
                           
                           <td>
              <button id="Del" type="button" class="btn btn-default"><i class="fal fa-trash"></i></button>
                           </td>
                        </tr>`;
       
       
       var u = $('#U'+UnitID).val();
                   if(u != UnitID){
                        document.getElementById('data-dt-first').innerHTML += table;
                   }
         $('#unit').val('');
   $('#rate').val('');
   $('#code').val('');
   $('#price1').val('');
   $('#price2').val('');
   $('#price3').val('');
   document.getElementById("add-data").style.display = "none";   

         var rate = [];
$('input[name^="Rate"]').each(function() {
    rate.push(this.value);
});
       
       
   $('input[name^="Rate"]').each(function(key) {

       if(rate[key] == 1){
             HIDEEE += 1 ;
       
          }else{
              HIDEEE += 0 ;
          }
       
        
       });
       
       if(parseFloat(HIDEEE) == 1){
             document.getElementById("SUBMIT").style.display = "block";     
       }else{
                document.getElementById("SUBMIT").style.display = "none";  
       }
             
       
       
       
   $('#data-dt-first').on('click', '#Del', function(e){
   $(this).closest('tr').remove(); 
        var HIDEEE=0;   
        $.fn.rowCount = function() {
   return $('tr', $(this).find('tbody')).length;
   };
   
   var rowctr = $('#samble').rowCount(); 
       
          var rate = [];
$('input[name^="Rate"]').each(function() {
    rate.push(this.value);
});
       
   if(rowctr == 0){
    document.getElementById("SUBMIT").style.display = "none";       
       
   }else{

     document.getElementById("SUBMIT").style.display = "block";   
   }    
       
    $('input[name^="Rate"]').each(function(key) {

       if(rate[key] == 1){
             HIDEEE += 1 ;
       
          }else{
              HIDEEE += 0 ;
          }
       
        
       });
       
       if(parseFloat(HIDEEE) == 1){
             document.getElementById("SUBMIT").style.display = "block";     
       }else{
                document.getElementById("SUBMIT").style.display = "none";  
       }
      
       
   
        
   })        
     
    
 
   }
   
   
   
</script>
<!-- Default Unit -->
<script>
   function DEFAULT(r){
       
       $('.default').val(0);
       
       $('#def'+r).val(1);
       
   }
</script>
<!-- Scanner  problem -->         
<script>
   $(".form-control").keypress(function(event){
   if (event.which == '10' || event.which == '13') {
   event.preventDefault();
   }
   });             
   
   
</script>

<!-- Check Name -->
<script>
    function CheckNAME(){
        
        var ARNAME = $('#ARNAME').val();
        
         $.ajax({
      url:'AddCheckName',
      method:'GET',
      data:{ARNAME:ARNAME},
      dataType:'json',
      success:function(data)
      {         
          
        if(data.Name == 1){
            document.getElementById('SUBMIT').style.display='none';  
        document.getElementById('AlertName').style.display='block';  
        }else{
           
             document.getElementById('SUBMIT').style.display='block';  
        document.getElementById('AlertName').style.display='none'; 
            
        }  
          
  
      }
     })
        
        
        
    }

function CheckNAMEENGLISH(){
        
        var ARNAME = $('#ENNAME').val();
        
         $.ajax({
      url:'AddCheckName',
      method:'GET',
      data:{ARNAME:ARNAME},
      dataType:'json',
      success:function(data)
      {         
          
        if(data.Name == 1){
            document.getElementById('SUBMIT').style.display='none';  
        document.getElementById('AlertName').style.display='block';  
        }else{
           
             document.getElementById('SUBMIT').style.display='block';  
        document.getElementById('AlertName').style.display='none'; 
            
        }  
          
  
      }
     })
        
        
        
    }
</script>

<!-- Calculate Tax -->
<script>
    $(document).ready(function(){
   
    fetch_customer_data();
     var TAX=$('#TAX').val();
    function fetch_customer_data(TAX = '')
    {
     $.ajax({
      url:'TaxPriceFilter',
      method:'GET',
      data:{TAX:TAX},
      dataType:'json',
      success:function(data)
      {
          
          var PriceWithTax=$('#PriceWithTax').val();
          
          if(parseFloat(data.rate) != 0){
          var Res= parseFloat(PriceWithTax) / parseFloat(data.rate) ;
          }else{
              
            var Res= parseFloat(PriceWithTax) ;   
          }
              
          $('#OriginalPrice').val(parseFloat(Res).toFixed(2));
          
      
      }
     })
    }
    
   $(document).on('change', '#TAX', function(){
     var TAX = $('#TAX').val();     
     fetch_customer_data(TAX);
    });
   
       $(document).on('keyup', '#PriceWithTax', function(){
     var TAX = $('#TAX').val();     
     fetch_customer_data(TAX);
    });  
        
     
        
        
   });   
    


</script>

<!-- Addition Product Table -->
<script>

      function InsertAddition() {
       var AdditionProduct=$('#AdditionProduct').val();
       var AdditionProductName=$('#AdditionProduct option:selected').text();
      

      

        var table = ` <tr> 
                                <td>
                            ${AdditionProductName}
          <input type="hidden" name="Additional_Product[]" value="${AdditionProduct}">
                                </td>
         

                                <td>
                    <button id="DelDepP" type="button" class="btn btn-default"><i class="fal fa-trash"></i></button>
                                </td>

                             </tr>`;
        
        document.getElementById("dataAddition").innerHTML += table;
      document.getElementById("addAdditionP").style.display = "none";

        $("#dataAddition").on("click", "#DelDepP", function (e) {
            $(this).closest("tr").remove();

        });
    }
    
    function AdditionPlus(){
        var AdditionProduct=$('#AdditionProduct').val();
        
        if (AdditionProduct != "") {
            document.getElementById("addAdditionP").style.display = "block";
        } 
        
        
         if (AdditionProduct == "") {
             document.getElementById("addAdditionP").style.display = "none";
        } 
        
    }




</script>

<!-- Variable_Aggregate -->
<script>
   $(document).ready(function(){
   
    fetch_customer_data();
   
    function fetch_customer_data(searchVariableAggregate = '')
    {
     $.ajax({
      url:'VariableAggregateFilter',
      method:'GET',
      data:{searchVariableAggregate:searchVariableAggregate},
      dataType:'json',
      success:function(data)
      {
       $('#dataVariableAggregate').html(data.table_data);
      }
     })
    }
    
   $(document).on('keyup', '#searchVariableAggregate', function(){
     var searchVariableAggregate = $(this).val();     
     fetch_customer_data(searchVariableAggregate);
    });
   
       
   });
</script>

<!-- Add Variable_Aggregate -->
<script>
   function FunVA(r) { 
       
             var P_Ar_Name = $("#P_Ar_Name"+r).val();
             var P_En_Name= $("#P_En_Name"+r).val();
             var Product = $("#Product"+r).val();
             var UnitID = $("#UnitAssem"+r).val();
             var UnitName = $("#UnitNameAssem"+r).val();
             var Barcode = $("#CodeAssem"+r).val();
             var LANG = $("#LANG").val();


             document.getElementById("AddVA"+r).style.display = "none";
             document.getElementById("RowVA"+r).style.display = "none";
          var LANG = $("#LANG").val();
       if(LANG == 'ar' ){ 
          var Nemo = P_Ar_Name ;
          }else{
             var Nemo = P_En_Name ;   
          }
      
             var markup = "<tr><td><input type='hidden' name='P_Ar_NameVA[]' value='"+P_Ar_Name+"'><input type='hidden' name='P_En_NameVA[]' value='"+P_En_Name+"'>" + Nemo + "</td><td><input type='hidden' name='P_CodeVA[]' value='"+Barcode+"'>" + Barcode + "</td><td><input type='hidden' name='UnitVA[]' value='"+UnitID+"'>" + UnitName + "</td><td>  <button id='DelVA"+r+"' type='button' class='btn btn-default'><i class='fal fa-trash'></i></button><input id='Product_IDVA"+r+"' type='hidden' name='ProductVA[]' value='"+Product+"'></td></tr>";
   
       
       if(LANG == 'ar'){
           var VAName='المتغير';
           var VAQty='الكميه';
        }else{
                      var VAName='Virable';
           var VAQty='Qty';
            
        }
       markup +="<tr id='rowVA"+r+"' style='background: #886ab5; color:white;'><td><lable>"+VAName+"</label><select  onchange='SubSay("+r+")' class='select2 form-control' id='VASelect"+r+"'></select></td><td><lable>"+VAName+"</label><select  class='select2 form-control' id='VASubSelect"+r+"'></select></td><td><lable>"+VAQty+"</label><input type='number' step='any' id='VAQty"+r+"' class='form-control' value='1'></td><td><button class='btn btn-primary' onclick='AddToVA("+r+")' type='button'><i class='fal fa-plus'></i></button></td></tr><tr id='BodyVA"+r+"'></tr>";
         
          
          
         
       
       
            var  Product_IDInp =$("#Product_IDVA"+r).val();
       
         if(Product != Product_IDInp){
             $("#data-dt-VariableAggregate").append(markup);
             
             
                  $.ajax({
                   url: 'VASelectFilterr',
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#VASelect'+r).empty();
   
                       $.each(data, function(key, value){
   
             $('#VASelect'+r).append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
            
                       
                                   var VA=$('#VASelect'+r).val();
                          $.ajax({
                   url: 'VASubSelectFilterr',
                   type:"GET",
                   data:{
                       VA:VA
                   },
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#VASubSelect'+r).empty();
   
                       $.each(data, function(key, value){
   
             $('#VASubSelect'+r).append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
            
                       
                               
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });       
                               
          
                       
                       
        
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });       
                       
          
             
             
         }
       
       
       
       
       
       
      
        $('#data-dt-VariableAggregate').on('click', '#DelVA'+r, function(e){
                $(this).closest('tr').remove(); 
                $('#rowVA'+r).remove(); 
                $('#BodyVA'+r).remove(); 
                    })  
       
       
     }   
    
    function Say(r){
        $.ajax({
                   url: 'VASelectFilterr',
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#VASelect'+r).empty();
   
                       $.each(data, function(key, value){
   
             $('#VASelect'+r).append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
            
                       
                                   var VA=$('#VASelect'+r).val();
                          $.ajax({
                   url: 'VASubSelectFilterr',
                   type:"GET",
                   data:{
                       VA:VA
                   },
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#VASubSelect'+r).empty();
   
                       $.each(data, function(key, value){
   
             $('#VASubSelect'+r).append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
            
                       
                               
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });       
                               
          
                       
                       
        
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });       
                       
                       
    }
    
    function SubSay(r){
        
                        var VA=$('#VASelect'+r).val();
                          $.ajax({
                   url: 'VASubSelectFilterr',
                   type:"GET",
                   data:{
                       VA:VA
                   },
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#VASubSelect'+r).empty();
   
                       $.each(data, function(key, value){
   
             $('#VASubSelect'+r).append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
            
                       
                               
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });       
                               
          
        
    }
    
    function  AddToVA(r){
        
         var VA=$('#VASelect'+r).val();  
         var VASub=$('#VASubSelect'+r).val();  
         var VAName=$('#VASelect'+r+' option:selected').text();  
         var VASubName=$('#VASubSelect'+r+' option:selected').text();  
         var VAQty=$('#VAQty'+r).val();  
        
        
       var markup = "<tr style='background: #d4a1cc;'><td><input type='hidden' name='ViraVA[]' value='"+VA+"'>" + VAName + "</td><td><input type='hidden' name='SubViraVA[]' value='"+VASub+"'>" + VASubName + "</td><td><input type='hidden' name='QtyVA[]' value='"+VAQty+"'><input type='hidden' name='ProductVAQty[]' value='"+r+"'>" + VAQty + "</td><td><i id='delVA"+r+"' class='fal fa-trash'></i></td></tr>";
        
          $("#BodyVA"+r).append(markup);
        
        
        
            $("#BodyVA"+r).on("click", "#delVA"+r, function (e) {
            $(this).closest("tr").remove();

        });
        
        
        
    }
  
    
    function AddPriceVA(){
        
              
               var PriceVASelect=$('#PriceVASelect').val();
               var PriceSubVASelect=$('#PriceSubVASelect').val();
       var PriceVASelectName=$('#PriceVASelect option:selected').text();
       var PriceSubVASelectName=$('#PriceSubVASelect option:selected').text();
      
 var PriceVA=$('#PriceVA').val();
 var OfferPriceVA=$('#OfferPriceVA').val();
      

        var table = ` <tr> 
                                <td>
                            ${PriceVASelectName}
          <input type="hidden" name="PriceVAMainV[]" value="${PriceVASelect}">
                                </td>

                    <td>
                            ${PriceSubVASelectName}
          <input type="hidden" name="PriceVASubV[]" value="${PriceSubVASelect}">
                                </td>


                    <td>
                            ${PriceVA}
          <input type="hidden" name="PriceVA[]" value="${PriceVA}">
                                </td>   

                          <td>
                            ${OfferPriceVA}
          <input type="hidden" name="OfferPriceVA[]" value="${PriceVA}">
                                </td>
         

                                <td>
                    <button id="DelPriceVA" type="button" class="btn btn-default"><i class="fal fa-trash"></i></button>
                                </td>

                             </tr>`;
        
        document.getElementById("data-price-VariableAggregate").innerHTML += table;
 

        $("#data-price-VariableAggregate").on("click", "#DelPriceVA", function (e) {
            $(this).closest("tr").remove();

        });
        
    }
    
        function SayPrice(){
        $.ajax({
                   url: 'VASelectFilterr',
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#PriceVASelect').empty();
   
                       $.each(data, function(key, value){
   
             $('#PriceVASelect').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
            
                       
                                   var VA=$('#PriceVASelect').val();
                          $.ajax({
                   url: 'VASubSelectFilterr',
                   type:"GET",
                   data:{
                       VA:VA
                   },
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#PriceSubVASelect').empty();
   
                       $.each(data, function(key, value){
   
             $('#PriceSubVASelect').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
            
                       
                               
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });       
                               
          
                       
                       
        
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });       
                       
                       
    }
    
    function SubSayPrice(){
        
                        var VA=$('#PriceVASelect').val();
                          $.ajax({
                   url: 'VASubSelectFilterr',
                   type:"GET",
                   data:{
                       VA:VA
                   },
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#PriceSubVASelect').empty();
   
                       $.each(data, function(key, value){
   
             $('#PriceSubVASelect').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
            
                       
                               
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });       
                               
          
        
    }
    

    


</script> 

<!-- Offers -->
<script>
    
function  ShowOffers(){
    
    var IFOffer =$('#IFOffer').val();
    
    if(parseFloat(IFOffer) == 1){
       document.getElementById('OFF1').style.display='block';
       document.getElementById('OFF2').style.display='block';
       document.getElementById('OFF3').style.display='block';
       
       }else{
       document.getElementById('OFF1').style.display='none';
       document.getElementById('OFF2').style.display='none';
       document.getElementById('OFF3').style.display='none';
       }
    
}

</script>

<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\erp\resources\views/admin/Stores/Products.blade.php ENDPATH**/ ?>
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class CreateAssetsSequence extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Create the sequence for assets
        DB::statement('CREATE SEQUENCE IF NOT EXISTS assets_arr_seq START 1');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Drop the sequence
        DB::statement('DROP SEQUENCE IF EXISTS assets_arr_seq');
    }
}

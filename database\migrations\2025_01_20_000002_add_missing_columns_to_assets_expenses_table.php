<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToAssetsExpensesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('assets_expenses', function (Blueprint $table) {
            // Add all the missing columns that are expected by the AssetsExpenses model
            $table->string('Code')->nullable();
            $table->date('Date')->nullable();
            $table->decimal('Amount', 15, 2)->nullable();
            $table->string('Asset')->nullable();
            $table->string('Safe')->nullable();
            $table->string('User')->nullable();
            $table->decimal('Draw', 15, 4)->nullable();
            $table->string('Coin')->nullable();
            $table->string('Cost_Center')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('assets_expenses', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn([
                'Code', 'Date', 'Amount', 'Asset', 'Safe', 'User', 'Draw', 'Coin', 'Cost_Center'
            ]);
        });
    }
}

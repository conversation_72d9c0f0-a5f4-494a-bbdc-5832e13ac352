<?php $__env->startSection('content'); ?>
<?php
use App\Models\ProductUnits;
use App\Models\ProductsPurchases;
use App\Models\ProductsStoresTransfers;
use App\Models\ProductsStartPeriods;
use App\Models\OutcomManufacturingModel;
use App\Models\StoresDefaultData;
use App\Models\DefaultDataShowHide;
use App\Models\FifoQty;
use App\Models\ProductMoves;
$Def=StoresDefaultData::orderBy('id','desc')->first();
$show=DefaultDataShowHide::orderBy('id','desc')->first();
?>

<?php
     function AverageCostGet($product,$code,$store){




                                $def=StoresDefaultData::orderBy('id','desc')->first();


           if($def->Cost_Price == 1){
                        $lastOperation=ProductMoves::orderBy('id','desc')->where('P_Code',$code)->where('Product',$product)->where('Store',$store)->first();
              $rr=ProductUnits::where('Product',$product)->where('Def',1)->first();

           if(!empty($lastOperation)){
           $AVERAGE = ($lastOperation->CostCurrent) /  ($lastOperation->Current) ;
           }else{
               if(!empty($rr->Price)){
           $AVERAGE = $rr->Price ;
               }else{
            $AVERAGE=1;
               }
           }

   }elseif($def->Cost_Price == 0){



          $PROO=ProductsPurchases::orderBy('id','desc')->where('Product_Code',$code)->where('Product',$product)->where('Store',$store)->first();
               $PROOStart=ProductsStartPeriods::orderBy('id','desc')->where('P_Code',$code)->where('Product',$product)->where('Store',$store)->first();
                    $rr=ProductUnits::where('Product',$product)->where('Def',1)->first();

                   if(!empty($PROO)){
                         $AVERAGE=$PROO->Price;
                   }else{


                       if(!empty($PROOStart)){

                             $AVERAGE = $PROOStart->Price ;
                       }else{
                          $AVERAGE = $rr->Price ;
                       }


                   }




           }elseif($def->Cost_Price == 2){

                $rr=ProductUnits::where('Product',$product)->where('Def',1)->first();

            $fifo =FifoQty::
                where('Store',$store)
                ->where('Product',$product)
                ->where('P_Code',$code)
                ->orderBy('Purchases_Date','asc')
                ->first();

                  if(empty($fifo)){

  $fifo =FifoQty::
                  where('Store',$store)
                ->where('Product',$product)
                ->where('PP_Code',$code)
               ->orderBy('Purchases_Date','asc')
                ->first();

if(empty($fifo)){

  $fifo =FifoQty::
             where('Store',$store)
                ->where('Product',$product)
                ->where('PPP_Code',$code)
                ->orderBy('Purchases_Date','asc')
                ->first();


if(empty($fifo)){

  $fifo =FifoQty::
           where('Store',$store)
                ->where('Product',$product)
                ->where('PPPP_Code',$code)
               ->orderBy('Purchases_Date','asc')
                ->first();

}

}

}

               if(!empty($fifo)){

                   if($fifo->Qty == 0){



         $ty=TestCost($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date);

        $AVERAGE = $ty ;

                   }else{
                  $AVERAGE = $fifo->Cost_Price ;
                   }


               }else{

                 $AVERAGE = $rr->Price ;

               }




           }



           return number_format((float)$AVERAGE, 2, '.', '') ;
    }



           function AverageCostGetT($product,$code){




                                $def=StoresDefaultData::orderBy('id','desc')->first();


           if($def->Cost_Price == 1){
                        $lastOperation=ProductMoves::orderBy('id','desc')->where('P_Code',$code)->where('Product',$product)->first();
              $rr=ProductUnits::where('Product',$product)->where('Def',1)->first();

           if(!empty($lastOperation)){
           $AVERAGE = ($lastOperation->CostCurrent) /  ($lastOperation->Current) ;
           }else{
               if(!empty($rr->Price)){
           $AVERAGE = $rr->Price ;
               }else{
            $AVERAGE=1;
               }
           }

   }elseif($def->Cost_Price == 0){



          $PROO=ProductsPurchases::orderBy('id','desc')->where('Product_Code',$code)->where('Product',$product)->first();
               $PROOStart=ProductsStartPeriods::orderBy('id','desc')->where('P_Code',$code)->where('Product',$product)->first();
                    $rr=ProductUnits::where('Product',$product)->where('Def',1)->first();

                   if(!empty($PROO)){
                         $AVERAGE=$PROO->Price;
                   }else{


                       if(!empty($PROOStart)){

                             $AVERAGE = $PROOStart->Price ;
                       }else{
                          $AVERAGE = $rr->Price ;
                       }


                   }




           }elseif($def->Cost_Price == 2){

                $rr=ProductUnits::where('Product',$product)->where('Def',1)->first();

            $fifo =FifoQty::

                where('Product',$product)
                ->where('P_Code',$code)
                ->orderBy('Purchases_Date','asc')
                ->first();

                  if(empty($fifo)){

  $fifo =FifoQty::

                where('Product',$product)
                ->where('PP_Code',$code)
               ->orderBy('Purchases_Date','asc')
                ->first();

if(empty($fifo)){

  $fifo =FifoQty::

                where('Product',$product)
                ->where('PPP_Code',$code)
                ->orderBy('Purchases_Date','asc')
                ->first();


if(empty($fifo)){

  $fifo =FifoQty::

                where('Product',$product)
                ->where('PPPP_Code',$code)
               ->orderBy('Purchases_Date','asc')
                ->first();

}

}

}

               if(!empty($fifo)){

                   if($fifo->Qty == 0){



         $ty=TestCost($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date);

        $AVERAGE = $ty ;

                   }else{
                  $AVERAGE = $fifo->Cost_Price ;
                   }


               }else{

                 $AVERAGE = $rr->Price ;

               }




           }



           return number_format((float)$AVERAGE, 2, '.', '') ;
    }



     function TestCost($store,$product,$code,$id,$Purchases_Date){


                   $rr=ProductUnits::where('Product',$product)->where('Def',1)->first();

            $fifo =FifoQty::
                where('Store',$store)
                ->where('Product',$product)
                ->where('P_Code',$code)
                ->where('id','!=',$id)
                     ->where('Purchases_Date','>',$Purchases_Date)
                ->first();

                  if(empty($fifo)){

  $fifo =FifoQty::
                  where('Store',$store)
                ->where('Product',$product)
                ->where('PP_Code',$code)
               ->where('id','!=',$id)
                     ->where('Purchases_Date','>',$Purchases_Date)
                ->first();

if(empty($fifo)){

  $fifo =FifoQty::
             where('Store',$store)
                ->where('Product',$product)
                ->where('PPP_Code',$code)
             ->where('id','!=',$id)
                     ->where('Purchases_Date','>',$Purchases_Date)
                ->first();


if(empty($fifo)){

  $fifo =FifoQty::
           where('Store',$store)
                ->where('Product',$product)
                ->where('PPPP_Code',$code)
               ->where('id','!=',$id)
                     ->where('Purchases_Date','>',$Purchases_Date)
                ->first();

}

}

}

               if(!empty($fifo)){

                   if($fifo->Qty == 0){



         $ty=TestCost($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date);

        $AVERAGE = $ty ;

                   }else{
                  $AVERAGE = $fifo->Cost_Price ;
                   }


               }else{

                 $AVERAGE = $rr->Price ;

               }

        return $AVERAGE ;


    }



       function MoreThanQty($store,$product,$code,$id,$Purchases_Date,$Qty){


                   $rr=ProductUnits::where('Product',$product)->where('Def',1)->first();

          $totCost=0;
            $fifo =FifoQty::
                where('Store',$store)
                ->where('Product',$product)
                ->where('P_Code',$code)
                ->where('id','!=',$id)
                     ->where('Purchases_Date','>',$Purchases_Date)
                ->first();

                  if(empty($fifo)){

  $fifo =FifoQty::
                  where('Store',$store)
                ->where('Product',$product)
                ->where('PP_Code',$code)
               ->where('id','!=',$id)
                     ->where('Purchases_Date','>',$Purchases_Date)
                ->first();

if(empty($fifo)){

  $fifo =FifoQty::
             where('Store',$store)
                ->where('Product',$product)
                ->where('PPP_Code',$code)
             ->where('id','!=',$id)
                     ->where('Purchases_Date','>',$Purchases_Date)
                ->first();


if(empty($fifo)){

  $fifo =FifoQty::
           where('Store',$store)
                ->where('Product',$product)
                ->where('PPPP_Code',$code)
               ->where('id','!=',$id)
                     ->where('Purchases_Date','>',$Purchases_Date)
                ->first();

}

}

}

               if(!empty($fifo)){


              if($fifo->Qty == 0){



         $Quntatity=FindQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date);


                    if($Quntatity == 0){


         $Quntatity=FindQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date);


                    }else{



                            if($Quntatity >= $Qty){

                    $totCost += $fifo->Cost_Price * $Qty ;

                }else{

                $res=$Qty - $Quntatity ;


                $totCost += $fifo->Cost_Price * $Quntatity ;

            $ResdiualCost=MoreThanQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date,$res);

                    $totCost +=$ResdiualCost;

                }


                    }



                   }else{


                if($fifo->Qty >= $Qty){

                    $totCost += $fifo->Cost_Price * $Qty ;

                }else{

                $res=$Qty - $fifo->Qty ;


                $totCost += $fifo->Cost_Price * $fifo->Qty ;

            $ResdiualCost=MoreThanQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date,$res);

                    $totCost +=$ResdiualCost;

                }



                   }


               }

        return  $totCost  ;


    }



       function FindQty($store,$product,$code,$id,$Purchases_Date){


                   $rr=ProductUnits::where('Product',$product)->where('Def',1)->first();

            $fifo =FifoQty::
                where('Store',$store)
                ->where('Product',$product)
                ->where('P_Code',$code)
                ->where('id','!=',$id)
                     ->where('Purchases_Date','>',$Purchases_Date)
                ->first();

                  if(empty($fifo)){

  $fifo =FifoQty::
                  where('Store',$store)
                ->where('Product',$product)
                ->where('PP_Code',$code)
               ->where('id','!=',$id)
                     ->where('Purchases_Date','>',$Purchases_Date)
                ->first();

if(empty($fifo)){

  $fifo =FifoQty::
             where('Store',$store)
                ->where('Product',$product)
                ->where('PPP_Code',$code)
             ->where('id','!=',$id)
                     ->where('Purchases_Date','>',$Purchases_Date)
                ->first();


if(empty($fifo)){

  $fifo =FifoQty::
           where('Store',$store)
                ->where('Product',$product)
                ->where('PPPP_Code',$code)
               ->where('id','!=',$id)
                     ->where('Purchases_Date','>',$Purchases_Date)
                ->first();

}

}

}

               if(!empty($fifo)){

                   if($fifo->Qty == 0){



         $Quntatity=FindQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date);



                   }else{
                  $qty = $fifo->Qty ;
                   }


               }

        return  $qty  ;


    }



?>


  <title><?php echo e(trans('admin.Items_Guide')); ?></title>
  <style>
      @media (max-width:600px){
          .mt{
              margin-top:10px;
          }
      }
      tbody{
          background:#8000803b;
      }
  </style>

    <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);"> <?php echo e(trans('admin.Stores')); ?> </a></li>
                        <li class="breadcrumb-item active"><?php echo e(trans('admin.Items_Guide')); ?> </li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span class="js-get-date"></span></li>
                    </ol>
                    <div class="subheader">

                    </div>
                    <div class="row">
                        <div class="col-xl-12">
                            <div id="panel-1" class="panel">
                                <div class="panel-hdr">
                                    <h2>
                                        <span class="fw-300"><i>  <?php echo e(trans('admin.Items_Guide')); ?></i></span>
                                    </h2>

                                    <div class="panel-toolbar">
                                        <button class="btn btn-primary btn-sm mx-3" data-toggle="dropdown">Table Style</button>
                                        <div
                                            class="dropdown-menu dropdown-menu-animated dropdown-menu-right position-absolute pos-top">
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-bordered" data-target="#dt-basic-example"> Bordered
                                                Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-sm"
                                                data-target="#dt-basic-example"> Smaller Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-dark"
                                                data-target="#dt-basic-example"> Table Dark </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-hover" data-target="#dt-basic-example"> Table Hover
                                            </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-stripe" data-target="#dt-basic-example"> Table
                                                Stripped </button>
                                            <div class="dropdown-divider m-0"></div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    tbody color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-tbody-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    thead color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-thead-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="panel-toolbar">
                                        <button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                                        <button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                                    </div>
                                </div>
                                <div class="panel-container show">
                                    <div class="panel-content">
                                    <div class="form-row">
                                        <form class="col-md-4" action="<?php echo e(url('TypeGuideFilter')); ?>" method="get">
                                            <div class="form-group ">
                                        <label class="form-label" for="">  <?php echo e(trans('admin.Product_Type')); ?> </label>
                   <select class="select2 form-control w-100" name="P_Type" onchange="this.form.submit()">
                                                     <option value=""><?php echo e(trans('admin.Product_Type')); ?> </option>
                                                     <option value="Completed"><?php echo e(trans('admin.Completed')); ?> </option>
                                                     <option value="Raw"><?php echo e(trans('admin.Raw')); ?> </option>
                                                     <option value="Service"><?php echo e(trans('admin.Service')); ?></option>
                                                     <option value="Subscribe"><?php echo e(trans('admin.Subscribe')); ?></option>
                                                    <option value="Assembly"><?php echo e(trans('admin.Assembly')); ?></option>
                                                     <option value="Industrial"><?php echo e(trans('admin.Industrial')); ?></option>
                                                  <option value="Single_Variable"><?php echo e(trans('admin.Single_Variable')); ?></option>
                                                 <option value="Duble_Variable"><?php echo e(trans('admin.Duble_Variable')); ?></option>
                                                <option value="Serial"><?php echo e(trans('admin.Serial')); ?></option>
                                                            </select>
                                            </div>
                                          </form>

                        <form class="col-md-4 mt" action="<?php echo e(url('NameGuideilter')); ?>" method="get">
                        <div class="row">
                                            <div class="form-group col-lg-10 col-10">
                        <label class="form-label"><?php echo e(trans('admin.Name')); ?></label>
                    <input type="text" class="form-control" name="Name" required>
                                            </div>
                                            <div class="form-group col-lg-2 col-2">
                           <button type="submit" class="btn btn-default" style="position: absolute;margin-top: 24px;margin-right: -24px;">    <i class="fal fa-search" onclick="this.form.submit()"></i>
                                                </button>

                                            </div>
                        </div>
                                        </form>
                     <form class="col-md-4" action="<?php echo e(url('CodeGuideFilter')); ?>" method="get">
                         <div class="row">
                                            <div class="form-group col-lg-10 col-10">
                        <label class="form-label"><?php echo e(trans('admin.Code')); ?></label>
                    <input type="text" class="form-control" name="Code" required>
                                            </div>
                                            <div class="form-group col-lg-2 col-2">
                                              <button class="btn btn-default" type="submit" style="position: absolute;margin-top: 24px;margin-right: -24px;">
                               <i  class="fal fa-search" onclick="this.form.submit()"></i>
                                                </button>
                                            </div>




                         </div>
                                        </form>







                                        </div>

                                                <form class="form-row mt-2"  action="<?php echo e(url('GroupGuideFilter')); ?>" method="get">

                                        <div class="col-md-5 mt">
                                            <div class="form-group">
                  <label class="form-label" for="">  <?php echo e(trans('admin.Group')); ?>  </label><span class="strick">*</span>
                              <select class="select2 form-control w-100" name="Group" >
                               <option value=""><?php echo e(trans('admin.Group')); ?></option>
                                                                <?php $__currentLoopData = $ItemsGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                          <option value="<?php echo e($group->id); ?>">

                                   <?php echo e(app()->getLocale() == 'ar' ?$group->Name :$group->NameEn); ?>

                                  </option>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </select>
                                            </div>
                                            </div>



                                                              <div class="col-md-5 mt">
                                            <div class="form-group">
                  <label class="form-label" for="">  <?php echo e(trans('admin.Brand')); ?>  </label><span class="strick">*</span>
                              <select class="select2 form-control w-100" name="Brand" >
                               <option value=""><?php echo e(trans('admin.Brand')); ?></option>
                                                                <?php $__currentLoopData = $Brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                          <option value="<?php echo e($brand->id); ?>">

                                   <?php echo e(app()->getLocale() == 'ar' ?$brand->Name :$brand->NameEn); ?>

                                  </option>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </select>
                                            </div>
                                        </div>


                                       <div class="col-md-2">
                                    <button type="submit" class="btn btn-default" style="position: absolute;margin-top: 24px;margin-right: 24px;">    <i class="fal fa-search"></i>
                                                </button>
                                            </div>


                                               </form>
                                         <div class="form-row mt-2">
                                             <div class="form-group col-lg-12 col-12">
              <button type="button" class="btn btn-danger" data-toggle="modal" data-target="#Change">   <?php echo e(trans('admin.Change_Price')); ?> </button>

                                            </div>
                                            </div>


                            <!-- Modal Change Price -->
                    <div class="modal fade" id="Change" tabindex="-1" role="dialog" aria-hidden="true">

                        <div class="modal-dialog modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title">
                                    <?php echo e(trans('admin.Change_Price')); ?>

                                    </h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>
                        <form action="<?php echo e(url('ChangeProductPrice')); ?>" method="post">
                                <?php echo csrf_field(); ?>
                                <div class="modal-body">
                                                       <div class="form-group">
                  <label class="form-label" for="">  <?php echo e(trans('admin.Group')); ?>  </label><span class="strick">*</span>
                              <select class="select2 form-control w-100" name="Group">
                               <option value=""><?php echo e(trans('admin.Group')); ?></option>
                                                                <?php $__currentLoopData = $ItemsGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                          <option value="<?php echo e($group->id); ?>">
                                 <?php echo e(app()->getLocale() == 'ar' ?$group->Name :$group->NameEn); ?>

                                  </option>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </select>
                                            </div>


                                                               <div class="form-group">
                  <label class="form-label" for="">  <?php echo e(trans('admin.Brand')); ?>  </label><span class="strick">*</span>
                              <select class="select2 form-control w-100" name="Brand">
                               <option value=""><?php echo e(trans('admin.Brand')); ?></option>
                                                                <?php $__currentLoopData = $Brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                          <option value="<?php echo e($brand->id); ?>">
                                 <?php echo e(app()->getLocale() == 'ar' ?$brand->Name :$brand->NameEn); ?>

                                  </option>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </select>
                                            </div>


                                            <div class="form-group">
 <label class="form-label" for="">  <?php echo e(trans('admin.Price_One')); ?>  </label><span class="strick">%</span>
        <input type="number" step="any" class="form-control" name="Price">
        </div>


                                                            <div class="form-group">
 <label class="form-label" for="">  <?php echo e(trans('admin.Price_Two')); ?>  </label><span class="strick">%</span>
        <input type="number" step="any" class="form-control" name="Price_Two">
        </div>


                                                    <div class="form-group">
 <label class="form-label" for="">  <?php echo e(trans('admin.Price_Three')); ?>  </label><span class="strick">%</span>
        <input type="number" step="any" class="form-control" name="Price_Three">
        </div>

                                                            <div class="form-group">
                  <label class="form-label" for="">  <?php echo e(trans('admin.Rounding')); ?>  </label><span class="strick">*</span>
                              <select class="select2 form-control w-100" name="Rounding" required>
                               <option value=""><?php echo e(trans('admin.Rounding')); ?></option>
                               <option value="1"><?php echo e(trans('admin.One_Num')); ?></option>
                               <option value="2"><?php echo e(trans('admin.Two_Num')); ?></option>
                               <option value="0"><?php echo e(trans('admin.Right_Num')); ?></option>

                                                            </select>
                                            </div>



                                </div>
                                <div class="modal-footer">
                 <button type="button" class="btn btn-secondary" data-dismiss="modal"> <?php echo e(trans('admin.Close')); ?></button>
                 <button type="submit" class="btn btn-primary" > <?php echo e(trans('admin.Sure')); ?></button>

                                </div>
                              </form>
                            </div>
                        </div>
                    </div>

                                        <!-- datatable start -->
                                        <div id="mobile-overflow">
                                        <table
                                            class="table table-bordered table-hover table-striped w-100 mobile-width th-width">
                                            <thead class="bg-highlight">
                                                <tr>
                                                    <th><?php echo e(trans('admin.Name')); ?> </th>
                                                    <th><?php echo e(trans('admin.Unit')); ?> </th>
                                                    <th><?php echo e(trans('admin.Code')); ?> </th>
                                                <?php if($Def->Guide_Product_Cost == 1): ?>
                                                    <th><?php echo e(trans('admin.Cost')); ?> </th>
                                                <?php endif; ?>
                                                    <th><?php echo e(trans('admin.Price_One')); ?> </th>
                                                    <th><?php echo e(trans('admin.Price_Two')); ?> </th>
                                                    <th><?php echo e(trans('admin.Price_Three')); ?> </th>
                                                    <?php if($show->Items_Guide_Store_Show == 1): ?>
                                                    <th><?php echo e(trans('admin.Show_Other_Store')); ?> </th>
                                                    <th><?php echo e(trans('admin.Store_Show')); ?> </th>
                                                    <?php endif; ?>
                                                    <th class="no-print"><?php echo e(trans('admin.Actions')); ?> </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php

                                $Units=ProductUnits::where('Product',$item->id)->get();
                                    ?>

                                        <?php $__currentLoopData = $Units; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $uni): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php
      $purchs=ProductsPurchases::where('Product',$uni->Product)->where('SmallCode',$uni->Barcode)->get()->sum('Total_Bf_Tax');

    $countPurchs=ProductsPurchases::where('Product',$uni->Product)->where('SmallCode',$uni->Barcode)->get()->sum('SmallQty');
$storesTransfer=ProductsStoresTransfers::where('Product',$uni->Product)->where('SmallCode',$uni->Barcode)->get()->sum('Total');

$storesTransferCount=ProductsStoresTransfers::where('Product',$uni->Product)->where('SmallCode',$uni->Barcode)->get()->sum('SmallTrans_Qty');

    $purchsStart=ProductsStartPeriods::where('Product',$uni->Product)->where('SmallCode',$uni->Barcode)->get()->sum('Total');

    $countStart=ProductsStartPeriods::where('Product',$uni->Product)->where('SmallCode',$uni->Barcode)->get()->sum('SmallQty');

$OUTCOME=OutcomManufacturingModel::where('Product',$uni->Product)->where('SmallCode',$uni->Barcode)->get()->sum('Cost');

$countOUTCOME=OutcomManufacturingModel::where('Product',$uni->Product)->where('SmallCode',$uni->Barcode)->get()->sum('SmallQty');

          if($countPurchs + $countStart + $storesTransferCount + $countOUTCOME   != 0){
              $ty= ( ($purchs + $purchsStart + $storesTransfer + $OUTCOME)) /  (($countPurchs + $countStart + $storesTransferCount + $countOUTCOME)) * $uni->Rate ;
            }else{
             $ty= ( ($purchs + $purchsStart + $storesTransfer + $OUTCOME)) * $uni->Rate  ;

            }



          $ty=AverageCostGetT($uni->Product,$uni->Barcode);









                                            ?>
                                                <tr>
                                                    <td>

                                 <?php echo e(app()->getLocale() == 'ar' ?$item->P_Ar_Name :$item->P_En_Name); ?>

                                                   </td>
                                                    <td>

             <?php if(!empty($uni->Unit) && $uni->Unit()->first()): ?>
                <?php echo e(app()->getLocale() == 'ar' ? $uni->Unit()->first()->Name : $uni->Unit()->first()->NameEn); ?>

             <?php elseif(!empty($uni->Unit)): ?>
                <?php echo e(trans('admin.Not_Found')); ?>

             <?php endif; ?>

                                                    </td>

                                    <td>
      <div class="form-group fill">

      <input type="text"  id="Code<?php echo e($uni->id); ?>" class="form-control" value="<?php echo e($uni->Barcode); ?>">

      </div>
    </td>


                               <?php if($Def->Guide_Product_Cost == 1): ?>
                                                       <td>

                                     <?php echo e(number_format((float)abs($ty), 2, '.', '')); ?>

                                                </td>
                                    <?php endif; ?>

                                                    <td>
      <div class="form-group fill">

          <?php if(!empty($uni->Price)): ?>
      <input type="number" step="any" id="Price_One<?php echo e($uni->id); ?>" class="form-control" value="<?php echo e($uni->Price); ?>">
          <?php else: ?>
         <input type="number" step="any" id="Price_One<?php echo e($uni->id); ?>" class="form-control" value="0">
          <?php endif; ?>
      </div>
    </td>

      <td>
      <div class="form-group fill">
                <?php if(!empty($uni->Price_Two)): ?>
      <input type="number" step="any" id="Price_Two<?php echo e($uni->id); ?>" class="form-control" value="<?php echo e($uni->Price_Two); ?>">
                 <?php else: ?>
        <input type="number" step="any" id="Price_Two<?php echo e($uni->id); ?>" class="form-control" value="0">
                    <?php endif; ?>

      </div>
    </td>
                      <td>
      <div class="form-group fill">
                <?php if(!empty($uni->Price_Three)): ?>
      <input type="number" step="any" id="Price_Three<?php echo e($uni->id); ?>" class="form-control" value="<?php echo e($uni->Price_Three); ?>">
                 <?php else: ?>
           <input type="number" step="any" id="Price_Three<?php echo e($uni->id); ?>" class="form-control" value="0">
                <?php endif; ?>
      </div>
    </td>
                         <?php if($show->Items_Guide_Store_Show == 1): ?>
            <td>

           <div class="form-group col-md-12">
                                       <label class="form-label" for=""><?php echo e(trans('admin.Show_Other_Store')); ?>  </label>
                                       <select class="select2 form-control w-100" id="Show_Other_Store<?php echo e($uni->id); ?>">
                                          <option value="0" <?php if($item->Show_Other_Store == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.NO')); ?></option>
                                          <option value="1" <?php if($item->Show_Other_Store == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?></option>

                                       </select>
                                    </div>


            </td>

                <td>
                    <div class="form-group col-md-12">
                                       <label class="form-label" for=""><?php echo e(trans('admin.Store_Show')); ?>  </label>
                                       <select class="select2 form-control w-100" id="Store_Show<?php echo e($uni->id); ?>" >
                                          <option value="0" <?php if($item->Store_Show == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.NO')); ?></option>
                                          <option value="1" <?php if($item->Store_Show == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Storee')); ?></option>
                                          <option value="2" <?php if($item->Store_Show == 2): ?> selected <?php endif; ?>><?php echo e(trans('admin.Price_List')); ?></option>
                                          <option value="3" <?php if($item->Store_Show == 3): ?> selected <?php endif; ?>><?php echo e(trans('admin.Both')); ?></option>
                                       </select>
                                    </div>
            </td>
                                        <?php endif; ?>

                                    <td class="no-print">
                    <button type="button" class="btn btn-default" onclick="Update(<?php echo e($uni->id); ?>)">
                              <?php echo e(trans('admin.Update')); ?>

                                        </button>
                                                    </td>

                                                </tr>
                                             <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                             <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                  <th><?php echo e(trans('admin.Name')); ?> </th>
                                                    <th><?php echo e(trans('admin.Unit')); ?> </th>
                                                    <th><?php echo e(trans('admin.Code')); ?> </th>
                                                           <?php if($Def->Guide_Product_Cost == 1): ?>
                                                    <th><?php echo e(trans('admin.Cost')); ?> </th>
                                                <?php endif; ?>
                                                    <th><?php echo e(trans('admin.Price_One')); ?> </th>
                                                    <th><?php echo e(trans('admin.Price_Two')); ?> </th>
                                                    <th><?php echo e(trans('admin.Price_Three')); ?> </th>
                                                              <?php if($show->Items_Guide_Store_Show == 1): ?>
                                                          <th><?php echo e(trans('admin.Show_Other_Store')); ?> </th>
                                                    <th><?php echo e(trans('admin.Store_Show')); ?> </th>
                                                    <?php endif; ?>

                                                     <th class="no-print"><?php echo e(trans('admin.Actions')); ?> </th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                        </div>

                                        <?php echo e($items->Links()); ?>

                                        <!-- datatable end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
   <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">

    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
    <script>
        $(document).ready(function()
        {
            $(function()
            {
                $('.select2').select2();

                $(".select2-placeholder-multiple").select2(
                {
                    placeholder: "Select State"
                });
                $(".js-hide-search").select2(
                {
                    minimumResultsForSearch: 1 / 0
                });
                $(".js-max-length").select2(
                {
                    maximumSelectionLength: 2,
                    placeholder: "Select maximum 2 items"
                });
                $(".select2-placeholder").select2(
                {
                    placeholder: "Select a state",
                    allowClear: true
                });

                $(".js-select2-icons").select2(
                {
                    minimumResultsForSearch: 1 / 0,
                    templateResult: icon,
                    templateSelection: icon,
                    escapeMarkup: function(elm)
                    {
                        return elm
                    }
                });

                function icon(elm)
                {
                    elm.element;
                    return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
                }

                $(".js-data-example-ajax").select2(
                {
                    ajax:
                    {
                        url: "https://api.github.com/search/repositories",
                        dataType: 'json',
                        delay: 250,
                        data: function(params)
                        {
                            return {
                                q: params.term, // search term
                                page: params.page
                            };
                        },
                        processResults: function(data, params)
                        {
                            // parse the results into the format expected by Select2
                            // since we are using custom formatting functions we do not need to
                            // alter the remote JSON data, except to indicate that infinite
                            // scrolling can be used
                            params.page = params.page || 1;

                            return {
                                results: data.items,
                                pagination:
                                {
                                    more: (params.page * 30) < data.total_count
                                }
                            };
                        },
                        cache: true
                    },
                    placeholder: 'Search for a repository',
                    escapeMarkup: function(markup)
                    {
                        return markup;
                    }, // let our custom formatter work
                    minimumInputLength: 1,
                    templateResult: formatRepo,
                    templateSelection: formatRepoSelection
                });

                function formatRepo(repo)
                {
                    if (repo.loading)
                    {
                        return repo.text;
                    }

                    var markup = "<div class='select2-result-repository clearfix d-flex'>" +
                        "<div class='select2-result-repository__avatar mr-2'><img src='" + repo.owner.avatar_url + "' class='width-2 height-2 mt-1 rounded' /></div>" +
                        "<div class='select2-result-repository__meta'>" +
                        "<div class='select2-result-repository__title fs-lg fw-500'>" + repo.full_name + "</div>";

                    if (repo.description)
                    {
                        markup += "<div class='select2-result-repository__description fs-xs opacity-80 mb-1'>" + repo.description + "</div>";
                    }

                    markup += "<div class='select2-result-repository__statistics d-flex fs-sm'>" +
                        "<div class='select2-result-repository__forks mr-2'><i class='fal fa-lightbulb'></i> " + repo.forks_count + " Forks</div>" +
                        "<div class='select2-result-repository__stargazers mr-2'><i class='fal fa-star'></i> " + repo.stargazers_count + " Stars</div>" +
                        "<div class='select2-result-repository__watchers mr-2'><i class='fal fa-eye'></i> " + repo.watchers_count + " Watchers</div>" +
                        "</div>" +
                        "</div></div>";

                    return markup;
                }

                function formatRepoSelection(repo)
                {
                    return repo.full_name || repo.text;
                }
            });
        });

    </script>
    <script>
        //_fnFeatureHtmlLength();
        $(document).ready(function () {
            // Setup - add a text input to each footer cell
            $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
            $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
                var title = $(this).text();
                $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search ' + title + '" />');


                $('input', this).on('keyup change', function () {
                    if (table.column(i).search() !== this.value) {
                        table
                            .column(i)
                            .search(this.value)
                            .draw();
                    }
                });
            });
            var table = $('#dt-basic-example').DataTable(
                {
                    // responsive: true,
                    orderCellsTop: true,
                    fixedHeader: true,
                    lengthChange: true,

                    dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

                    buttons: [
                        {
                            extend: 'pageLength',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'colvis',
                            text: 'Column Visibility',
                            titleAttr: 'Col visibility',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            titleAttr: 'Generate PDF',
                            className: 'btn-outline-danger btn-sm mr-1'
                        },
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            titleAttr: 'Generate Excel',
                            className: 'btn-outline-success btn-sm mr-1'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            titleAttr: 'Generate CSV',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'copyHtml5',
                            text: 'Copy',
                            titleAttr: 'Copy to clipboard',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            titleAttr: 'Print Table',
                            className: 'btn-outline-primary btn-sm'
                        }
                    ],
                });
            $('.js-thead-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
            });

            $('.js-tbody-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
            });

        });

    </script>


<script>
  function Update(r){
    var P =$('#Price_One'+r).val();
    var PP =$('#Price_Two'+r).val();
    var PPP =$('#Price_Three'+r).val();
    var Code =$('#Code'+r).val();
    var Show_Other_Store =$('#Show_Other_Store'+r).val();
    var Store_Show =$('#Store_Show'+r).val();

        $.ajax({
      url:'UpdatePrice/'+r+'/'+P+'/'+PP+'/'+PPP+'/'+Code,
      method:'GET',
            data:{
              Show_Other_Store:Show_Other_Store,
                Store_Show:Store_Show
            },
      dataType:'json',
      success:function(data)
      {

        alert('Updated');

      }
     })
  }

</script>


<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\erp\resources\views/admin/Stores/ItemsGuide.blade.php ENDPATH**/ ?>
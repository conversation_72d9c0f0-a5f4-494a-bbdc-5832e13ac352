@extends('admin.index')
@section('content')
    @php
use App\Models\JournalizingDetails;
use App\Models\CompanyData;
$Def=CompanyData::orderBy('id','desc')->first();
@endphp

 <title>{{trans('admin.Journalizing')}}</title>

     <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb no-print">
                        <li class="breadcrumb-item"><a href="javascript:void(0);">{{trans('admin.Accounts')}}</a></li>
                        <li class="breadcrumb-item active"> {{trans('admin.Journalizing')}} </li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                class="js-get-date"></span></li>
                    </ol>

                    <!-- data entry -->

                    <div class="row hide-table">
                        <div class="col-xl-12">
                            <div id="panel-1" class="panel">

                                <div class="modal-body">
                                    <div data-size="A4">
                                        <div class="row invoive-info">
                                            <div style="text-align: center;" class="col-md-4 col-sm-4 col-4 invoice-client-info">
                                                <div class="style-info">
                                                    <h1 style="text-align: center;" class="m-0">
                                               @if(!empty($Def->Name))
                      {{app()->getLocale() == 'ar' ?$Def->Name :$Def->NameEn}}
                      @else
                       {{trans('admin.Ost')}}
                      @endif
                                                    </h1>
                              <h3 style="text-align: center;" class="m-10">
                         @if(!empty($Def->Print_Text))
               {{app()->getLocale() == 'ar' ?$Def->Print_Text :$Def->Print_Text_En}}
                      @endif
                                                    </h3>
                                                </div>
                                            </div>
                                            <div style="text-align: center;" class="col-md-4 col-sm-4 col-4 invoice-client-info">
                                                <h1>{{trans('admin.Journalizing')}}</h1>
                                            </div>
                                   <div style="text-align: center;" class="col-md-4 col-sm-4 col-4 invoice-client-info">
                         <div class="style-info">
                       @if(!empty($Def->Logo))
    <img style="height: 50px; width: 150px;" class="img-fluid" src="{{URL::to($Def->Logo)}}" alt="Logo" />

                    @else
 <img style="height: 50px; width: 150px;" class="img-fluid" src="https://Osterp.com/site/img/theme/logo.png" alt="Logo" />
                      @endif

                                       </div>
                                            </div>
                                        </div>
                                        <hr />
                                        <div class="row"></div>

                                        <div class="row">
                                            <div class="col-md-3 col-3">
                                                <h5> {{trans('admin.Code')}} : <span class="style-data">
                                            {{$item->Code}}
                                                </span></h5>
                                            </div>
                                            <div class="col-md-3 col-3">
                               <h5> {{trans('admin.Date')}}  : <span  class="style-data">{{$item->Date}}</span></h5>
                                            </div>
                                            <div class="col-md-3 col-3">
                                                <h5> {{trans('admin.Coin')}}  : <span  class="style-data">


                                        @if($item->Coin()->first())
                                            {{app()->getLocale() == 'ar' ? $item->Coin()->first()->Arabic_Name : $item->Coin()->first()->English_Name}}
                                        @else
                                            {{trans('admin.Not_Found')}}
                                        @endif
                                                    </span></h5>
                                            </div>
                                            <!--<div class="col-md-3 col-3">-->
                                            <!--    <h5> {{trans('admin.Draw')}}   : <span  class="style-data"> {{$item->Draw}}    </span></h5>-->
                                            <!--</div>-->

                                        </div>
                                        <div class="row">
                                     <!--       <div class="col-md-3 col-3">-->
                                     <!--<h5> {{trans('admin.Cost_Center')}} : <span class="style-data">-->
                                     <!--    @if(!empty($item->Cost_Center))        -->
                                     <!--                   {{$item->Cost_Center()->first()->Arabic_Name}}-->
                                     <!--               @endif-->
                                     <!--    </span></h5>	-->
                                     <!--       </div>-->
                                            <div class="col-md-6 col-6">
                                                <h5> {{trans('admin.Notes')}}   : <span  class="style-data">
                                                {{$item->Note}}
                                                    </span></h5>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="table-responsive-lg">
                                                    <table class="table table-bordered mt-5">
                                                        <thead>
                                                            <tr>
                                                                <th colspan="2" class="border-top-0 table-scale-border-bottom fw-700"> {{trans('admin.Debitor')}}</th>
                                                                <th colspan="2" class="border-top-0 table-scale-border-bottom fw-700"> {{trans('admin.Creditor')}}</th>
                                                                <th class="border-top-0 table-scale-border-bottom fw-700">  {{trans('admin.Account_Code')}}</th>
                                                                <th colspan="2"  class="border-top-0 table-scale-border-bottom fw-700">  {{trans('admin.Account_Name')}}</th>
                                                                <th class="border-top-0 table-scale-border-bottom fw-700"> {{trans('admin.Statement')}}</th>
                                                            </tr>
                                                        </thead>


                                                        <tbody>
                                                    @foreach($details as $detail)
                                                            <tr>
                                                                <td colspan="2">{{$detail->Debitor}}</td>
                                                                <td colspan="2">{{$detail->Creditor}}</td>
                                                                <td>
                                                                    @if($detail->Account()->first())
                                                                        {{$detail->Account()->first()->Code}}
                                                                    @else
                                                                        {{trans('admin.Not_Found')}}
                                                                    @endif
                                                                </td>
                                                                <td colspan="2" >
                                                                    @if($detail->Account()->first())
                                                                        {{app()->getLocale() == 'ar' ? $detail->Account()->first()->Name : $detail->Account()->first()->NameEn}}
                                                                    @else
                                                                        {{trans('admin.Not_Found')}}
                                                                    @endif
                                                                </td>
                                                                <td>{{$detail->Statement}}</td>
                                                            </tr>
                                                @endforeach
                                                             <tr>

                                                                <td>{{trans('admin.Total_Debitor')}}</td>
                                                                <td>{{$item->Total_Debaitor}}</td>
                                                                <td>{{trans('admin.Total_Creditor')}}</td>
                                                                <td>{{$item->Total_Creditor}}</td>
                                                                <!--<td colspan="2" style="background:#e9e9e957;">{{trans('admin.Capital')}}</td>-->

                <!--<td colspan="2" style="background:#e9e9e957;">{{$item->Total_Debaitor - $item->Total_Creditor}}</td>-->
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                                 <div  class="col-md-6 col-sm-6 col-6 invoice-client-info text-center">
                                                    <p class="mt-2 text-muted mb-0">

                                                    </p>
                                                </div>
                                                <div class="col-md-6 col-sm-6 col-6 invoice-client-info text-center" >

                                       @php
                                    $x=$item->Code;
                                    $y=DNS1D::getBarcodePNG($x, 'C39');
                                    @endphp
   <img src="data:image/png;base64,{{$y}}" id="barcode"  class="height-3 mt-1 " />

                                                </div>


                                        </div>
                                    </div>
                                </div>
                                    <div class="row">

                                             @if(!empty($Def->Print_Text_Footer))

                              {!! app()->getLocale() == 'ar' ?$Def->Print_Text_Footer :$Def->Print_Text_Footer_En !!}
                      @endif


                                        </div>
                                              <div class="row">

                                             @if(!empty($Def->Seal))

                   <img src="{{URL::to($Def->Seal)}}" class="img-fluid"  />
                      @endif


                                        </div>
                                <div class="modal-footer">
                  <a href="{{url('OstAdmin')}}" class="btn btn-secondary" data-dismiss="modal">{{trans('admin.Back')}}</a>
             <button type="button" class="btn btn-default" onclick="window.print()"><i class="fal fa-print"></i></button>
                                </div>

                            </div>
                        </div>
         </div>
</main>




@endsection
@push('js')
    <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/datagrid/datatables/datatables.bundle.css')}}">
    <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/formplugins/summernote/summernote.css')}}">
    <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/formplugins/select2/select2.bundle.css')}}">


    <style>
        th{
            width:135px!important;
        }
    </style>

 <script src="{{asset('Admin/js/datagrid/datatables/datatables.export.js')}}"></script>
    <script src="{{asset('Admin/js/datagrid/datatables/datatables.bundle.js')}}"></script>
    <script src="{{asset('Admin/js/formplugins/summernote/summernote.js')}}"></script>
    <script src="{{asset('Admin/js/formplugins/select2/select2.bundle.js')}}"></script>
    <script>
        //_fnFeatureHtmlLength();
        $(document).ready(function () {
            // Setup - add a text input to each footer cell
            $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
            $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
                var title = $(this).text();
                $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search ' + title + '" />');

                $('input', this).on('keyup change', function () {
                    if (table.column(i).search() !== this.value) {
                        table
                            .column(i)
                            .search(this.value)
                            .draw();
                    }
                });
            });
            var table = $('#dt-basic-example').DataTable(
                {
                    responsive: true,
                    orderCellsTop: true,
                    fixedHeader: true,
                    lengthChange: true,

                    dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

                    buttons: [
                        {
                            extend: 'pageLength',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'colvis',
                            text: 'Column Visibility',
                            titleAttr: 'Col visibility',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            titleAttr: 'Generate PDF',
                            className: 'btn-outline-danger btn-sm mr-1'
                        },
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            titleAttr: 'Generate Excel',
                            className: 'btn-outline-success btn-sm mr-1'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            titleAttr: 'Generate CSV',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'copyHtml5',
                            text: 'Copy',
                            titleAttr: 'Copy to clipboard',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            titleAttr: 'Print Table',
                            className: 'btn-outline-primary btn-sm'
                        }
                    ],
                });
            $('.js-thead-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
            });

            $('.js-tbody-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
            });

        });

    </script>

  <script>
    $(document).ready(function()
    {
        $(function()
        {
            $('.select2').select2();

            $(".select2-placeholder-multiple").select2(
            {
                placeholder: "Select State"
            });
            $(".js-hide-search").select2(
            {
                minimumResultsForSearch: 1 / 0
            });
            $(".js-max-length").select2(
            {
                maximumSelectionLength: 2,
                placeholder: "Select maximum 2 items"
            });
            $(".select2-placeholder").select2(
            {
                placeholder: "Select a state",
                allowClear: true
            });

            $(".js-select2-icons").select2(
            {
                minimumResultsForSearch: 1 / 0,
                templateResult: icon,
                templateSelection: icon,
                escapeMarkup: function(elm)
                {
                    return elm
                }
            });

            function icon(elm)
            {
                elm.element;
                return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
            }

            $(".js-data-example-ajax").select2(
            {
                ajax:
                {
                    url: "https://api.github.com/search/repositories",
                    dataType: 'json',
                    delay: 250,
                    data: function(params)
                    {
                        return {
                            q: params.term, // search term
                            page: params.page
                        };
                    },
                    processResults: function(data, params)
                    {
                        // parse the results into the format expected by Select2
                        // since we are using custom formatting functions we do not need to
                        // alter the remote JSON data, except to indicate that infinite
                        // scrolling can be used
                        params.page = params.page || 1;

                        return {
                            results: data.items,
                            pagination:
                            {
                                more: (params.page * 30) < data.total_count
                            }
                        };
                    },
                    cache: true
                },
                placeholder: 'Search for a repository',
                escapeMarkup: function(markup)
                {
                    return markup;
                }, // let our custom formatter work
                minimumInputLength: 1,
                templateResult: formatRepo,
                templateSelection: formatRepoSelection
            });

            function formatRepo(repo)
            {
                if (repo.loading)
                {
                    return repo.text;
                }

                var markup = "<div class='select2-result-repository clearfix d-flex'>" +
                    "<div class='select2-result-repository__avatar mr-2'><img src='" + repo.owner.avatar_url + "' class='width-2 height-2 mt-1 rounded' /></div>" +
                    "<div class='select2-result-repository__meta'>" +
                    "<div class='select2-result-repository__title fs-lg fw-500'>" + repo.full_name + "</div>";

                if (repo.description)
                {
                    markup += "<div class='select2-result-repository__description fs-xs opacity-80 mb-1'>" + repo.description + "</div>";
                }

                markup += "<div class='select2-result-repository__statistics d-flex fs-sm'>" +
                    "<div class='select2-result-repository__forks mr-2'><i class='fal fa-lightbulb'></i> " + repo.forks_count + " Forks</div>" +
                    "<div class='select2-result-repository__stargazers mr-2'><i class='fal fa-star'></i> " + repo.stargazers_count + " Stars</div>" +
                    "<div class='select2-result-repository__watchers mr-2'><i class='fal fa-eye'></i> " + repo.watchers_count + " Watchers</div>" +
                    "</div>" +
                    "</div></div>";

                return markup;
            }

            function formatRepoSelection(repo)
            {
                return repo.full_name || repo.text;
            }
        });
    });

</script>
<script>
    var autoSave = $('#autoSave');
    var interval;
    var timer = function()
    {
        interval = setInterval(function()
        {
            //start slide...
            if (autoSave.prop('checked'))
                saveToLocal();

            clearInterval(interval);
        }, 3000);
    };

    //save
    var saveToLocal = function()
    {
        localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
        console.log("saved");
    }

    //delete
    var removeFromLocal = function()
    {
        localStorage.removeItem("summernoteData");
        $('#saveToLocal').summernote('reset');
    }

    $(document).ready(function()
    {
        //init default
        $('.js-summernote').summernote(
        {
            height: 200,
            tabsize: 2,
            placeholder: "Type here...",
            dialogsFade: true,
            toolbar: [
                ['style', ['style']],
                ['font', ['strikethrough', 'superscript', 'subscript']],
                ['font', ['bold', 'italic', 'underline', 'clear']],
                ['fontsize', ['fontsize']],
                ['fontname', ['fontname']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['height', ['height']]
                ['table', ['table']],
                ['insert', ['link', 'picture', 'video']],
                ['view', ['fullscreen', 'codeview', 'help']]
            ],
            callbacks:
            {
                //restore from localStorage
                onInit: function(e)
                {
                    $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
                },
                onChange: function(contents, $editable)
                {
                    clearInterval(interval);
                    timer();
                }
            }
        });

        //load emojis
        $.ajax(
        {
            url: 'https://api.github.com/emojis',
            async: false
        }).then(function(data)
        {
            window.emojis = Object.keys(data);
            window.emojiUrls = data;
        });

        //init emoji example
        $(".js-hint2emoji").summernote(
        {
            height: 100,
            toolbar: false,
            placeholder: 'type starting with : and any alphabet',
            hint:
            {
                match: /:([\-+\w]+)$/,
                search: function(keyword, callback)
                {
                    callback($.grep(emojis, function(item)
                    {
                        return item.indexOf(keyword) === 0;
                    }));
                },
                template: function(item)
                {
                    var content = emojiUrls[item];
                    return '<img src="' + content + '" width="20" /> :' + item + ':';
                },
                content: function(item)
                {
                    var url = emojiUrls[item];
                    if (url)
                    {
                        return $('<img />').attr('src', url).css('width', 20)[0];
                    }
                    return '';
                }
            }
        });

        //init mentions example
        $(".js-hint2mention").summernote(
        {
            height: 100,
            toolbar: false,
            placeholder: "type starting with @",
            hint:
            {
                mentions: ['jayden', 'sam', 'alvin', 'david'],
                match: /\B@(\w*)$/,
                search: function(keyword, callback)
                {
                    callback($.grep(this.mentions, function(item)
                    {
                        return item.indexOf(keyword) == 0;
                    }));
                },
                content: function(item)
                {
                    return '@' + item;
                }
            }
        });

    });

</script>
<script type="text/javascript">


    $(".show-table").click(function(){
        $(".hide-table").show();
    });

</script>

	<style>
	@media print {
		body * {
			visibility: hidden;
		}
		.modal-body * {
			visibility: visible;
			overflow: visible;
		}



	}
	</style>



@endpush

@extends('admin.index')
@section('content')
@php
use App\Models\DefaultDataShowHide;
$show=DefaultDataShowHide::orderBy('id','desc')->first();
@endphp
<title>{{trans('admin.Insurance_Paper')}}</title>
     <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);">{{trans('admin.Accounts')}}</a></li>
                        <li class="breadcrumb-item">{{trans('admin.Insurance_Paper')}}</li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                class="js-get-date"></span></li>
                    </ol>


                    <!-- data entry -->
            <div class="panel-container show">
                                    <div class="panel-content">


     <form action="{{url('InsurancePaperSechduleFilter')}}" method="get">

                                        <div class="form-row">
                                            <div class="form-group col-md-3 col-4-print">
                                                <label class="form-label" for="simpleinput">{{trans('admin.From')}}</label>
                                                <input type="date" id="from" value="{{date('Y-m-d')}}" name="From" class="form-control">
                                            </div>
                                            <div class="form-group col-md-3 col-4-print">
                                                <label class="form-label" for="simpleinput">{{trans('admin.To')}}</label>
                                                <input type="date" id="to" value="{{date('Y-m-d')}}" name="To" class="form-control">
                                            </div>

                                                 <div class="form-group col-lg-3">
                                    <label class="form-label" for=""> {{trans('admin.Account_Name')}} </label>
                    <select class="js-data-example-ajax form-control w-100 Acc" name="Account"  >
                                    </select>
                                </div>


                                                             <div class="form-group col-md-3 col-4-print">
                                                <label class="form-label" for="simpleinput">{{trans('admin.Due_Date')}}</label>
                                                <input type="date" id="Due_Date"  name="Due_Date" class="form-control">
                                            </div>


                                            <div class="form-group col-md-3 col-4-print">
                                                <label class="form-label" for="simpleinput">{{trans('admin.Code')}}</label>
                                                <input type="text" id="Code"  name="Code" class="form-control">
                                            </div>
                                            <div class="buttons m-2">
                                            <button type="submit" class="btn btn-primary"><i class="fal fa-search"></i></button>
                                                </div>
          </div>
                                        </form>
                                    </div>
                                </div>

                    <div class="row">
                        <div class="col-xl-12">
                            <div id="panel-1" class="panel">
                                <div class="panel-hdr">
                                   <h2>

                                      {{trans('admin.Insurance_Paper')}}
                                   </h2>

                                    <div class="panel-toolbar">
                                          @can('اضافه وصل امانه')
                                    <button type="button" class="btn btn-default btn-sm margin-btn" data-toggle="modal" data-target="#default-example-modal-center-add">
                                        {{trans('admin.AddNew')}}</button>
                                       @endcan
                                        <button class="btn btn-primary btn-sm" data-toggle="dropdown">Table
                                            Style</button>
                                        <div
                                            class="dropdown-menu dropdown-menu-animated dropdown-menu-right position-absolute pos-top">
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-bordered" data-target="#dt-basic-example"> Bordered
                                                Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-sm"
                                                data-target="#dt-basic-example"> Smaller Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-dark"
                                                data-target="#dt-basic-example"> Table Dark </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-hover" data-target="#dt-basic-example"> Table Hover
                                            </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-stripe" data-target="#dt-basic-example"> Table
                                                Stripped </button>
                                            <div class="dropdown-divider m-0"></div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    tbody color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-tbody-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    thead color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-thead-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="panel-container show">
                                            <span id="ex"> @include('admin.layouts.messages')</span>
                                    <div class="panel-content">

                                        <!-- datatable start -->
                                        <div style="overflow:auto;">
                                        <table id="dt-basic-example"
                                            class="table table-bordered table-hover table-striped" style="width:100%;">
                                            <thead class="bg-highlight">
                                                <tr>
                                                    <th>{{trans('admin.Code')}}</th>
                                                    <th>{{trans('admin.Date')}}</th>
                                                    <th> {{trans('admin.Account_Name')}}</th>
                                                    <th> {{trans('admin.From_Ar_Name')}}</th>
                                                    <th> {{trans('admin.To_Ar_Name')}}</th>
                                                    <th> {{trans('admin.From_En_Name')}}</th>
                                                    <th> {{trans('admin.To_En_Name')}}</th>
                                                    <th> {{trans('admin.Due_Date')}}</th>
                                                    <th> {{trans('admin.Amount')}}</th>

                                                     <th>  {{trans('admin.Data')}}</th>
                                                     <th>  {{trans('admin.File')}}</th>
                                                    <th style="padding: 13px 36px;"> {{trans('admin.Actions')}}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                @foreach($items as $item)
                                                <tr>
                                                    <td>{{$item->Code}}</td>
                                                    <td>{{$item->Date}}</td>
                                              <td>
                                                         @if($item->Account()->first())
                                                             {{app()->getLocale() == 'ar' ? $item->Account()->first()->Name : $item->Account()->first()->NameEn}}
                                                         @else
                                                             {{trans('admin.Not_Found')}}
                                                         @endif
                                                    </td>
                                                    <td>{{$item->From}}</td>
                                                    <td>{{$item->To}}</td>
                                                    <td>{{$item->FromEn}}</td>
                                                    <td>{{$item->ToEn}}</td>

                                                      <td>{{$item->Due_Date}}</td>

                                                    <td>{{$item->Amount}}</td>



                                                       <td>
                                                          <button type="button" class="btn btn-default" data-toggle="modal" data-target="#show-data{{$item->id}}">
                                                                        {{trans('admin.Data')}}
                                                        </button>
                                                     </td>
                                                                  <td>
                                @if(!empty($item->File))
                            <a href="{{URL::to($item->File)}}" class="btn btn-primary" dowmload><i class="fal fa-download"></i></a>
                                        @endif
                                    </td>
                                                    <td>

                    @if($item->Status == 0)
       @can('حذف وصل امانه')
        <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Delete{{$item->id}}">
                                                                        <i class="fal fa-trash"></i>
                                                        </button>
                                                        @endcan

                   @can('استلام وصل امانه')
        <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Recived{{$item->id}}">
                                                                       <i class="fal fa-check"></i>
                                                        </button>
                                            @endcan
                                    @endif
                                                 <button type="button" class="btn btn-default" data-toggle="modal" data-target="#print{{$item->id}}">
                                                                       <i class="fal fa-print"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                              @endforeach



                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                  <th>{{trans('admin.Code')}}</th>
                                                    <th>{{trans('admin.Date')}}</th>
                                                    <th> {{trans('admin.Account_Name')}}</th>
                                            <th> {{trans('admin.From_Ar_Name')}}</th>
                                                    <th> {{trans('admin.To_Ar_Name')}}</th>
                                                    <th> {{trans('admin.From_En_Name')}}</th>
                                                    <th> {{trans('admin.To_En_Name')}}</th>
                                                    <th> {{trans('admin.Due_Date')}}</th>
                                                    <th> {{trans('admin.Amount')}}</th>

                                                     <th> {{trans('admin.Data')}}</th>
                                                     <th> {{trans('admin.File')}}</th>
                                                    <th> {{trans('admin.Actions')}}</th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                        </div>
                                       {{$items->Links()}}
                                    </div>
                                </div>
                            </div>
                        </div>
         </div>
</main>


                 <!-- Modal Add -->
     <div class="modal fade" id="default-example-modal-center-add" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h4 class="modal-title">
                                   {{trans('admin.AddNew')}}
                                </h4>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                </button>
                            </div>
                    <form action="{{url('AddInsurancePaper')}}" method="post" enctype="multipart/form-data">
                        {!! csrf_field() !!}
                              @honeypot
                            <div class="modal-body">

                               <div class="row">
                                <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"> {{trans('admin.Code')}}</label>
                                    <input type="text"  value="{{$Code}}" disabled class="form-control">
                                    <input type="hidden" name="Code"  value="{{$Code}}">
                                </div>
                                 @if(auth()->guard('admin')->user()->emp == 0)
                      <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput">{{trans('admin.Date')}}</label>
                                    <input type="date" name="Date" value="{{date('Y-m-d')}}" class="form-control" required>
                                </div>
                                        @else

                                         @if(auth()->guard('admin')->user()->Date == 1)
                                   <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput">{{trans('admin.Date')}}</label>
                                    <input type="date" name="Date" value="{{date('Y-m-d')}}" class="form-control" required>
                                </div>
                                        @else
                                 <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput">{{trans('admin.Date')}}</label>
                                    <input type="date" name="Date" value="{{date('Y-m-d')}}" class="form-control" required readonly>
                                </div>
                                        @endif

                                        @endif

                                    <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput">{{trans('admin.From_Ar_Name')}}</label>
                                    <input type="text" name="From" value="{{old('From')}}" class="form-control" required>
                                </div>

                                            <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput">{{trans('admin.To_Ar_Name')}}</label>
                                    <input type="text" name="To" value="{{old('To')}}" class="form-control" required>
                                </div>


                                              <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput">{{trans('admin.From_En_Name')}}</label>
                                    <input type="text" name="FromEn" value="{{old('FromEn')}}" class="form-control" >
                                </div>

                                            <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput">{{trans('admin.To_En_Name')}}</label>
                                    <input type="text" name="ToEn" value="{{old('ToEn')}}" class="form-control" >
                                </div>

                                <div class="form-group col-lg-3">
                                    <label class="form-label" for=""> {{trans('admin.Account_Name')}} </label>
                    <select class="js-data-example-ajax form-control w-100 Acc" name="Account"  required>
                                    </select>
                                </div>



                                <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput">{{trans('admin.Due_Date')}}</label>
                                    <input type="date" name="Due_Date" value="{{old('Due_Date')}}" class="form-control" required>
                                </div>

                                <div class="form-group col-lg-3">
                                    <label class="form-label" for="">{{trans('admin.Coin')}}   </label>
                                    <select class="select2 form-control w-100" name="Coin"  required>
                                          <option value="">{{trans('admin.Coin')}}</option>
                                     @foreach($Coins as $coin)
                           <option value="{{$coin->id}}" >
        {{app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name}}
                                        </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput">{{trans('admin.Draw')}}</label>
                      <input type="number" step="any" name="Draw" value="1" class="form-control" required />
                                </div>

                                <div class="form-group col-lg-3">
                                    <label class="form-label" for="">{{trans('admin.Cost_Center')}}   </label>
                                    <select class="select2 form-control w-100" name="Cost_Center">
                                        <option value="">{{trans('admin.Cost_Center')}} </option>
                                     @foreach($CostCenters as $cost)
                                        <option value="{{$cost->id}}">
        {{app()->getLocale() == 'ar' ?$cost->Arabic_Name :$cost->English_Name}}
                                        </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput">{{trans('admin.Amount')}}</label>
                                    <input type="number" step="any" value="{{old('Amount')}}" name="Amount" class="form-control" required>
                                </div>
                                <div class="form-group col-lg-12">
                                    <label class="form-label" for="simpleinput">{{trans('admin.Notes')}}</label>
                                    <input type="text" name="Note" value="{{old('Note')}}" class="form-control">
                                </div>


                                         @if($show && $show->Show_File_InsurancePaper == 1)
                                     <div class="form-group col-lg-12">
                                    <label class="form-label" for="simpleinput">{{trans('admin.File')}}</label>
                                    <input type="file" name="File"  class="form-control">
                                </div>
                                @endif


                               </div>
                            </div>
                            <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">{{trans('admin.Close')}}</button>
                                <button type="submit" class="btn btn-primary">{{trans('admin.Save')}}</button>
                            </div>
                        </form>
                        </div>
                    </div>
                </div>


@foreach($items as $item)

                 <!-- Modal Delete -->
                 <div class="modal fade" id="Delete{{$item->id}}" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered " role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h4 class="modal-title">
                                        {{trans('admin.Delete')}}
                                </h4>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                   {{trans('admin.RUSWDT')}} <strong>{{$item->Code}}</strong>
                            </div>
                            <div class="modal-footer">
                   <button type="button" class="btn btn-secondary" data-dismiss="modal"> {{trans('admin.No')}}</button>
                   <a href="{{url('DeleteInsurancePaper/'.$item->id)}}"  class="btn btn-primary"> {{trans('admin.Yes')}}</a>
                            </div>
                        </div>
                    </div>
                </div>
                 <!--Modal show data-->
                 <div class="modal fade" id="show-data{{$item->id}}" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h4 class="modal-title">
                                       {{trans('admin.Data')}}
                                </h4>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div style="overflow:auto">
                                                                        <table
                                            class="table table-bordered table-hover table-striped" >
                                            <thead class="bg-highlight">
                                                <tr>

                                                    <th> {{trans('admin.Coin')}}</th>
                                                    <th> {{trans('admin.Draw')}}</th>
                                                    <th> {{trans('admin.Coin_Amount')}}</th>
                                                    <th> {{trans('admin.Cost_Center')}}</th>
                                                    <th> {{trans('admin.Notes')}}</th>
                                                    <th> {{trans('admin.Status')}}</th>
                                                    <th> {{trans('admin.Bank')}}</th>
                                                    <th> {{trans('admin.User')}}</th>

                                                </tr>
                                            </thead>
                                            <tbody>

                                                <tr>


                                                                   <td>
                                                         @if($item->Coin()->first())
                                                             {{app()->getLocale() == 'ar' ? $item->Coin()->first()->Arabic_Name : $item->Coin()->first()->English_Name}}
                                                         @else
                                                             {{trans('admin.Not_Found')}}
                                                         @endif
                                                    </td>
                                                     <td>{{$item->Draw}}</td>
                                                     <td>{{$item->Draw * $item->Amount}}</td>
                                                     <td>

                                         @if(!empty($item->Cost_Center) && $item->Cost_Center()->first())
                                             {{app()->getLocale() == 'ar' ? $item->Cost_Center()->first()->Arabic_Name : $item->Cost_Center()->first()->English_Name}}
                                         @elseif(!empty($item->Cost_Center))
                                             {{trans('admin.Not_Found')}}
                                         @endif
                                                    </td>

                                                     <td>{{$item->Note}}</td>
                                                     <td>
                                                    @if($item->Status == 0)
                                                         {{trans('admin.Waiting')}}

                                                    @elseif($item->Status == 1)

                                                       {{trans('admin.Recived')}}  <br>

                                                         @endif
                                                    </td>
                                                         <td>
                                                   @if(!empty($item->Bank) && $item->Bank()->first())
                                                       {{app()->getLocale() == 'ar' ? $item->Bank()->first()->Name : $item->Bank()->first()->NameEn}}
                                                   @elseif(!empty($item->Bank))
                                                       {{trans('admin.Not_Found')}}
                                                   @endif
                                                    </td>
                                                     <td>
                                          @if($item->User()->first())
                                              {{app()->getLocale() == 'ar' ? $item->User()->first()->name : $item->User()->first()->nameEn}}
                                          @else
                                              {{trans('admin.Not_Found')}}
                                          @endif
                                                    </td>

                                                </tr>




                                            </tbody>
                                            <tfoot>
                                                <tr>

                                                    <th> {{trans('admin.Coin')}}</th>
                                                    <th> {{trans('admin.Draw')}}</th>
                                                    <th> {{trans('admin.Coin_Amount')}}</th>
                                                    <th> {{trans('admin.Cost_Center')}}</th>
                                                    <th> {{trans('admin.Notes')}}</th>
                                                    <th> {{trans('admin.Status')}}</th>
                                                    <th> {{trans('admin.Bank')}}</th>
                                                    <th> {{trans('admin.User')}}</th>

                                                </tr>
                                            </tfoot>
                                        </table>
                            </div>
                            </div>
                            <div class="modal-footer">

                   <button type="button" class="btn btn-secondary" data-dismiss="modal"> {{trans('admin.Close')}}</button>

                            </div>
                        </div>
                    </div>
                </div>

      <!-- Modal Recived -->
                 <div class="modal fade" id="Recived{{$item->id}}" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered " role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h4 class="modal-title">
                                        {{trans('admin.Recived')}}
                                </h4>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                </button>
                            </div>
                    <form action="{{url('RecivedInurance/'.$item->id)}}" method="post">
                        {!! csrf_field() !!}
                            <div class="modal-body">

                                           <div class="form-group col-lg-12">
                                    <label class="form-label" for="">{{trans('admin.Bank')}}   </label>
                        <select class="js-data-example-ajax form-control w-100"  name="Bank" id="Bank" required>
                                    </select>
                                </div>

                                         <div class="modal-footer">
                   <button type="button" class="btn btn-secondary" data-dismiss="modal"> {{trans('admin.Close')}}</button>
                   <button type="submit" class="btn btn-secondary"> {{trans('admin.Recived')}}</button>
                            </div>
                            </div>
                   </form>
                        </div>
                    </div>
                </div>
      <!-- Modal Print -->
                 <div class="modal fade" id="print{{$item->id}}" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog modal-xl modal-dialog-centered " role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h4 class="modal-title">
{{trans('admin.Insurance_Paper')}}
                                </h4>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                </button>
                            </div>

                            <div class="modal-body">
                               <div class="row">
                                   <div class="col-lg-12">
                                       <div><span>
                                           {{trans('admin.I_received')}}
                                             :</span>
                                       @if($item->Account()->first())
                                           {{app()->getLocale() == 'ar' ? $item->Account()->first()->Name : $item->Account()->first()->NameEn}}
                                       @else
                                           {{trans('admin.Not_Found')}}
                                       @endif
                                       </div>
                                   </div>
                                   <div class="col-lg-6 col-6">
                                       <div><span>     {{trans('admin.Resident')}} :</span> ..............</div>
                                   </div>
                                   <div class="col-lg-3 col-3">
                                       <div><span> {{trans('admin.Center')}} :</span>............</div>
                                   </div>
                                   <div class="col-lg-3 col-3">
                                       <div><span> {{trans('admin.Governrate')}} :</span>.............</div>
                                   </div>
                                   <div class="col-lg-4 col-4">
                                       <div><span>  {{trans('admin.ID_Number')}} :</span>........</div>
                                   </div>
                                   <div class="col-lg-4 col-4">
                                       <div><span>{{trans('admin.Personal_family')}}  {{trans('admin.Coming_From')}} :</span>.........</div>
                                   </div>
                                   <div class="col-lg-4 col-4">
                                       <div><span>{{trans('admin.Dately')}} :</span>.........</div>
                                   </div>
                                   <div class="col-lg-6 col-6">
                                       <div><span> {{trans('admin.From_Mr')}} :</span>
                                           {{app()->getLocale() == 'ar' ?$item->From :$item->FromEn}}   </div>
                                   </div>
                                   <div class="col-lg-2 col-2">
                                       <div><span> {{trans('admin.Resident')}} :</span>........</div>
                                   </div>
                                   <div class="col-lg-2 col-2">
                                       <div><span> {{trans('admin.Center')}} :</span>..........</div>
                                   </div>
                                   <div class="col-lg-2 col-2">
                                       <div><span>{{trans('admin.Governrate')}} :</span>...........</div>
                                   </div>
                                         <div class="col-lg-4 col-4">
                                       <div><span>  {{trans('admin.ID_Number')}} :</span>........</div>
                                   </div>
                                   <div class="col-lg-4 col-4">
                                       <div><span> {{trans('admin.Amount_Of')}} :</span>{{$item->Amount}}  {{trans('admin.Just')}}</div>
                                   </div>
                                   <div class="col-lg-4 col-4">
                                       <div><span class="bg-color">
                                           @php
                                               try {
                                                   if(class_exists('NumberFormatter')) {
                                                       if(app()->getLocale() == 'ar' ){
                                                           $f = new NumberFormatter("ar", NumberFormatter::SPELLOUT);
                                                           echo $f->format(round($item->Amount));
                                                       } else {
                                                           $f = new NumberFormatter("en", NumberFormatter::SPELLOUT);
                                                           echo $f->format(round($item->Amount));
                                                       }
                                                   } else {
                                                       // Fallback when NumberFormatter is not available
                                                       echo round($item->Amount) . ' ' . (app()->getLocale() == 'ar' ? 'فقط' : 'only');
                                                   }
                                               } catch (Exception $e) {
                                                   // Fallback in case of any error
                                                   echo round($item->Amount) . ' ' . (app()->getLocale() == 'ar' ? 'فقط' : 'only');
                                               }
                                           @endphp
                                           </span>

                                    </div>
                                   </div>
                                   <div class="col-lg-4 col-4">
                                       <div>   ...................... </div>
                                   </div>
                                   <div class="col-lg-12">
                                       <div><span> {{trans('admin.Insurance_Letter')}} :</span> {{app()->getLocale() == 'ar' ?$item->To :$item->En}} </div>
                                   </div>
                                    <div class="col-lg-4 col-4">
                                       <div><span> {{trans('admin.Resident')}} :</span>........</div>
                                   </div>
                                   <div class="col-lg-4 col-4">
                                       <div><span>{{trans('admin.Center')}} :</span>..........</div>
                                   </div>
                                   <div class="col-lg-4 col-4">
                                       <div><span>{{trans('admin.Governrate')}} :</span>...........</div>
                                   </div>
                                              <div class="col-lg-4 col-4">
                                       <div><span>  {{trans('admin.ID_Number')}} :</span>........</div>
                                   </div>

                                   <div class="col-lg-12 ">
                              {{trans('admin.Insurance_Letter2')}}
                                   </div>
                                   <div class="col-lg-12 text-center">

                                   {{trans('admin.Insurance_Letter3')}}       ...
                                   </div>
                                   <div class="col-lg-8 col-8">

                                   </div>
                                   <div class="col-lg-4 col-4 text-center">
                                       <p> {{trans('admin.Recipients')}} </p>
                                       <div><span class="bg-color">..........</span></div>
                                   </div>
                               </div>

                                         <div class="modal-footer">
                   <button type="button" class="btn btn-secondary" data-dismiss="modal"> {{trans('admin.Close')}}</button>
                   <button type="submit" class="btn btn-secondary"  onclick="window.print()">{{trans('admin.Print')}}</button>
                            </div>
                            </div>

                        </div>
                    </div>
                </div>



@endforeach



@endsection

@push('js')
   <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/datagrid/datatables/datatables.bundle.css')}}">
    <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/formplugins/summernote/summernote.css')}}">
    <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/formplugins/select2/select2.bundle.css')}}">

 <script src="{{asset('Admin/js/datagrid/datatables/datatables.export.js')}}"></script>
    <script src="{{asset('Admin/js/datagrid/datatables/datatables.bundle.js')}}"></script>
    <script src="{{asset('Admin/js/formplugins/summernote/summernote.js')}}"></script>
    <script src="{{asset('Admin/js/formplugins/select2/select2.bundle.js')}}"></script>
    <script>
        //_fnFeatureHtmlLength();
        $(document).ready(function () {
            // Setup - add a text input to each footer cell
            $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
            $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
                var title = $(this).text();
                $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search ' + '" />');

                $('input', this).on('keyup change', function () {
                    if (table.column(i).search() !== this.value) {
                        table
                            .column(i)
                            .search(this.value)
                            .draw();
                    }
                });
            });
            var table = $('#dt-basic-example').DataTable(
                {
                    // responsive: true,
                    orderCellsTop: true,
                    fixedHeader: true,
                    lengthChange: true,

                    dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

                    buttons: [
                        {
                            extend: 'pageLength',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'colvis',
                            text: 'Column Visibility',
                            titleAttr: 'Col visibility',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            titleAttr: 'Generate PDF',
                            className: 'btn-outline-danger btn-sm mr-1'
                        },
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            titleAttr: 'Generate Excel',
                            className: 'btn-outline-success btn-sm mr-1'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            titleAttr: 'Generate CSV',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'copyHtml5',
                            text: 'Copy',
                            titleAttr: 'Copy to clipboard',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            titleAttr: 'Print Table',
                            className: 'btn-outline-primary btn-sm'
                        }
                    ],
                });
            $('.js-thead-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
            });

            $('.js-tbody-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
            });

        });

    </script>
     <script>
        var autoSave = $('#autoSave');
        var interval;
        var timer = function()
        {
            interval = setInterval(function()
            {
                //start slide...
                if (autoSave.prop('checked'))
                    saveToLocal();

                clearInterval(interval);
            }, 3000);
        };

        //save
        var saveToLocal = function()
        {
            localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
            console.log("saved");
        }

        //delete
        var removeFromLocal = function()
        {
            localStorage.removeItem("summernoteData");
            $('#saveToLocal').summernote('reset');
        }

        $(document).ready(function()
        {
            //init default
            $('.js-summernote').summernote(
            {
                height: 200,
                tabsize: 2,
                placeholder: "Type here...",
                dialogsFade: true,
                toolbar: [
                    ['style', ['style']],
                    ['font', ['strikethrough', 'superscript', 'subscript']],
                    ['font', ['bold', 'italic', 'underline', 'clear']],
                    ['fontsize', ['fontsize']],
                    ['fontname', ['fontname']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['height', ['height']]
                    ['table', ['table']],
                    ['insert', ['link', 'picture', 'video']],
                    ['view', ['fullscreen', 'codeview', 'help']]
                ],
                callbacks:
                {
                    //restore from localStorage
                    onInit: function(e)
                    {
                        $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
                    },
                    onChange: function(contents, $editable)
                    {
                        clearInterval(interval);
                        timer();
                    }
                }
            });

            //load emojis
            $.ajax(
            {
                url: 'https://api.github.com/emojis',
                async: false
            }).then(function(data)
            {
                window.emojis = Object.keys(data);
                window.emojiUrls = data;
            });

            //init emoji example
            $(".js-hint2emoji").summernote(
            {
                height: 100,
                toolbar: false,
                placeholder: 'type starting with : and any alphabet',
                hint:
                {
                    match: /:([\-+\w]+)$/,
                    search: function(keyword, callback)
                    {
                        callback($.grep(emojis, function(item)
                        {
                            return item.indexOf(keyword) === 0;
                        }));
                    },
                    template: function(item)
                    {
                        var content = emojiUrls[item];
                        return '<img src="' + content + '" width="20" /> :' + item + ':';
                    },
                    content: function(item)
                    {
                        var url = emojiUrls[item];
                        if (url)
                        {
                            return $('<img />').attr('src', url).css('width', 20)[0];
                        }
                        return '';
                    }
                }
            });

            //init mentions example
            $(".js-hint2mention").summernote(
            {
                height: 100,
                toolbar: false,
                placeholder: "type starting with @",
                hint:
                {
                    mentions: ['jayden', 'sam', 'alvin', 'david'],
                    match: /\B@(\w*)$/,
                    search: function(keyword, callback)
                    {
                        callback($.grep(this.mentions, function(item)
                        {
                            return item.indexOf(keyword) == 0;
                        }));
                    },
                    content: function(item)
                    {
                        return '@' + item;
                    }
                }
            });

        });

    </script>


   <!-- Search Selecet -->
 <script>
        $(document).ready(function()
        {
            $(function()
            {
                $('.select2').select2();

                $(".select2-placeholder-multiple").select2(
                {
                    placeholder: "Select State"
                });
                $(".js-hide-search").select2(
                {
                    minimumResultsForSearch: 1 / 0
                });
                $(".js-max-length").select2(
                {
                    maximumSelectionLength: 2,
                    placeholder: "Select maximum 2 items"
                });
                $(".select2-placeholder").select2(
                {
                    placeholder: "Select a state",
                    allowClear: true
                });

                $(".js-select2-icons").select2(
                {
                    minimumResultsForSearch: 1 / 0,
                    templateResult: icon,
                    templateSelection: icon,
                    escapeMarkup: function(elm)
                    {
                        return elm
                    }
                });

                function icon(elm)
                {
                    elm.element;
                    return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
                }


      $('#Bank').select2({
    placeholder: "select...",
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllSafes',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };

            	console.log(data);

        },
        data: function (params) {
          var query = {
            search: params.term
          };
          if (params.term == "*") query.items = [];
          return { json: JSON.stringify( query ) }
        }
    }
  });


$('#Bank').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});




  $('.Acc').select2({
    placeholder: "select...",
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllSubAccounts',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };

            	console.log(data);

        },
          data: function (params) {


                   var query = {
                            search: params.term,
                        };


              $.ajax({
                              url: 'AllSubAccountsJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('.Acc').empty();
                                  $.each(data, function(key, value){

                         $('.Acc').append('<option value="'+ key +'">' + value + '</option>');

                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });



        }
    }
  });


$('.Acc').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});




            });
        });



    </script>

<style>
@media print {

  #js-page-content {
    display: none;
  }

      .close {
    display: none;
  }

}
</style>
@endpush


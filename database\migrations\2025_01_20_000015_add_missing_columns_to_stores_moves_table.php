<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToStoresMovesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('stores_moves', function (Blueprint $table) {
            // Add all the missing columns that are expected by the StoresMoves model
            $table->date('Date')->nullable();
            $table->string('Code')->nullable();
            $table->string('Time')->nullable();
            $table->string('Branch')->nullable(); // Foreign key to branches
            $table->string('Store')->nullable(); // Foreign key to stores
            $table->string('Safe')->nullable(); // Foreign key to acccounting_manuals
            $table->string('Type')->nullable();
            $table->string('TypeEn')->nullable();
            $table->string('Cost_Center')->nullable(); // Foreign key to cost_centers
            $table->string('User')->nullable(); // Foreign key to admins
            $table->string('Coin')->nullable(); // Foreign key to coins
            $table->text('Note')->nullable();
            $table->decimal('Total_Qty', 15, 4)->nullable();
            $table->decimal('Total_Price', 15, 2)->nullable();
            $table->string('Account')->nullable(); // Foreign key to acccounting_manuals
            $table->string('Ship')->nullable();
            $table->string('ID')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('stores_moves', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn([
                'Date', 'Code', 'Time', 'Branch', 'Store', 'Safe', 'Type', 'TypeEn', 'Cost_Center',
                'User', 'Coin', 'Note', 'Total_Qty', 'Total_Price', 'Account', 'Ship', 'ID'
            ]);
        });
    }
}

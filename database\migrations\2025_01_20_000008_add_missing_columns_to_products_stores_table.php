<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToProductsStoresTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('products_stores', function (Blueprint $table) {
            // Add all the missing columns that are expected by the ProductsStores model
            $table->string('P_Ar_Name')->nullable();
            $table->string('P_En_Name')->nullable();
            $table->string('V_Name')->nullable();
            $table->string('VV_Name')->nullable();
            $table->string('P_Code')->nullable();
            $table->date('Exp_Date')->nullable();
            $table->string('Product')->nullable(); // Foreign key to products
            $table->string('Store')->nullable(); // Foreign key to stores
            $table->string('V1')->nullable(); // Foreign key to sub_virables
            $table->string('V2')->nullable(); // Foreign key to sub_virables
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('products_stores', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn([
                'P_Ar_Name', 'P_En_Name', 'V_Name', 'VV_Name', 'P_Code', 'Exp_Date', 
                'Product', 'Store', 'V1', 'V2'
            ]);
        });
    }
}

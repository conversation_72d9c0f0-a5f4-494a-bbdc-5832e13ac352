<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToProductsStoresTransfersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('products_stores_transfers', function (Blueprint $table) {
            // Add all the missing columns that are expected by the ProductsStoresTransfers model
            $table->string('P_Ar_Name')->nullable();
            $table->string('P_En_Name')->nullable();
            $table->string('V_Name')->nullable();
            $table->string('VV_Name')->nullable();
            $table->string('P_Code')->nullable();
            $table->decimal('Price', 15, 2)->nullable();
            $table->decimal('OldPrice', 15, 2)->nullable();
            $table->decimal('Av_Qty', 15, 4)->nullable();
            $table->string('SmallCode')->nullable();
            $table->decimal('Trans_Qty', 15, 4)->nullable();
            $table->decimal('Original_Trans_Qty', 15, 4)->nullable();
            $table->decimal('SmallTrans_Qty', 15, 4)->nullable();
            $table->decimal('Total', 15, 2)->nullable();
            $table->string('ST_ID')->nullable(); // Foreign key to stors_transfers
            $table->string('Product')->nullable(); // Foreign key to products
            $table->string('V1')->nullable(); // Foreign key to sub_virables
            $table->string('V2')->nullable(); // Foreign key to sub_virables
            $table->string('Unit')->nullable(); // Foreign key to measuerments
            $table->string('To_Store')->nullable(); // Foreign key to stores
            $table->decimal('CostPrice', 15, 2)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('products_stores_transfers', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn([
                'P_Ar_Name', 'P_En_Name', 'V_Name', 'VV_Name', 'P_Code', 'Price', 'OldPrice', 'Av_Qty',
                'SmallCode', 'Trans_Qty', 'Original_Trans_Qty', 'SmallTrans_Qty', 'Total', 'ST_ID',
                'Product', 'V1', 'V2', 'Unit', 'To_Store', 'CostPrice'
            ]);
        });
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToProductMovesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('product_moves', function (Blueprint $table) {
            // Add all the missing columns that are expected by the ProductMoves model
            $table->date('Date')->nullable();
            $table->string('Type')->nullable();
            $table->string('TypeEn')->nullable();
            $table->string('Bill_Num')->nullable();
            $table->decimal('Incom', 15, 4)->nullable();
            $table->decimal('Outcom', 15, 4)->nullable();
            $table->decimal('Current', 15, 4)->nullable();
            $table->string('P_Ar_Name')->nullable();
            $table->string('P_En_Name')->nullable();
            $table->string('P_Code')->nullable();
            $table->string('Unit')->nullable(); // Foreign key to measuerments
            $table->string('Group')->nullable(); // Foreign key to items_groups
            $table->string('Store')->nullable(); // Foreign key to stores
            $table->string('Product')->nullable(); // Foreign key to products
            $table->string('V1')->nullable(); // Foreign key to sub_virables
            $table->string('V2')->nullable(); // Foreign key to sub_virables
            $table->string('User')->nullable(); // Foreign key to admins
            $table->decimal('CostIn', 15, 2)->nullable();
            $table->decimal('CostOut', 15, 2)->nullable();
            $table->decimal('CostCurrent', 15, 2)->nullable();
            $table->decimal('QTY', 15, 4)->nullable();
            $table->string('Brand')->nullable(); // Foreign key to brands
            $table->string('Safe')->nullable(); // Foreign key to acccounting_manuals
            $table->string('Branch')->nullable(); // Foreign key to branches
            $table->decimal('SalePrice', 15, 2)->nullable();
            $table->decimal('ProductPrice', 15, 2)->nullable();
            $table->string('Delegate')->nullable(); // Foreign key to employess
            $table->string('Payment_Method')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('product_moves', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn([
                'Date', 'Type', 'TypeEn', 'Bill_Num', 'Incom', 'Outcom', 'Current', 'P_Ar_Name', 'P_En_Name',
                'P_Code', 'Unit', 'Group', 'Store', 'Product', 'V1', 'V2', 'User', 'CostIn', 'CostOut',
                'CostCurrent', 'QTY', 'Brand', 'Safe', 'Branch', 'SalePrice', 'ProductPrice', 'Delegate',
                'Payment_Method'
            ]);
        });
    }
}

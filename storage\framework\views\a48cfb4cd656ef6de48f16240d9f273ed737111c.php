<style>
.header-icon {
    color: #666666;
    height: 1.125rem !important;
    display: block;
    line-height: 4.125rem;
    text-decoration: none;
    position: relative;
}
.page-header .badge-icon {
    left: 1.5625rem;
    top: -0.5rem!important;
}
.animated-btn{
     position: relative;
  animation-name: example;
  animation-duration: 4s;
  animation-iteration-count: infinite;
}
@keyframes  example {
  0%   {background-color:#da8f55; left:0px; top:0px;}
  50%  {background-color:#ff6f00; left:5px; top:0px;}
  100% {background-color:#fbb681; left:0px; top:0px;}
}
    .over-flow{
        overflow:auto;
    }
    .table-width{
        width:140%;
    }
    @media  print{
            .over-flow{
            overflow:visible;
        }
        .table-width{
            width:100%;
        }  
         .panel-content{
            padding:0;
        }
        .form-group {
        margin-bottom:0;
        }
        @page{
            margin:0;
            padding:0;
        }
        input[type=date].form-control{
            font-weight: 700;
            color:black;
        }
       
    }
</style>

<?php $__env->startSection('content'); ?>
<?php
    use App\Models\PaymentVoucherDetails;
use App\Models\CompanyData;
$Def=CompanyData::orderBy('id','desc')->first();
?>
  <title><?php echo e(trans('admin.Vendor_Account_Statement')); ?></title>
<style>
    .over-flow{
        overflow:auto;
    }
    .table-width{
        width:137%;
    }
    @media  print{
            .over-flow{
            overflow:unset;
        }
        .table-width{
            width:100%;
        }  
               table {
        height:auto !important;
        width:100% !important;
        margin-top:-10px;

    }
    .labelss{
     font-size:14px !important;
     color:black;
    }
    th, td{
        width:auto !important;
       
    }
    }
</style>

   <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb no-print">
                       <li class="breadcrumb-item"><a href="javascript:void(0);"> <?php echo e(trans('admin.Accounts_Reports')); ?></a></li>
                        <li class="breadcrumb-item active"> <?php echo e(trans('admin.Vendor_Account_Statement')); ?> </li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                class="js-get-date"></span></li>
                    </ol>
                    
                    <!-- data entry -->
                     <div class="row ">
                        <div class="col-xl-12">
                            <div id="panel-1" class="panel d-none d-print-block" style="margin-top:30px;">
                      <div class="row invoive-info ">
                                            <div style="text-align: center;" class="col-md-4 col-sm-4 col-4 invoice-client-info">
                                                <div class="style-info">
                                                    <h1 style="text-align: center;" class="m-0">
                                               <?php if(!empty($Def->Name)): ?>
                      <?php echo e($Def->Name); ?>

                      <?php else: ?>
                       <?php echo e(trans('admin.Ost')); ?>

                      <?php endif; ?>         
                                                    </h1>
                              <h3 style="text-align: center;" class="m-10">
                         <?php if(!empty($Def->Print_Text)): ?>
                      <?php echo e($Def->Print_Text); ?>

                      <?php endif; ?>                
                                                    </h3>
                                                </div>
                                            </div>
                                            <div style="text-align: center;" class="col-md-4 col-sm-4 col-4 invoice-client-info">
                                                <h1> <?php echo e(trans('admin.Vendor_Account_Statement')); ?></h1>
                                            </div>
                                   <div style="text-align: center;" class="col-md-4 col-sm-4 col-4 invoice-client-info">
                         <div class="style-info">
                       <?php if(!empty($Def->Logo)): ?>         
    <img style="height: 50px; width: 150px;" class="img-fluid" src="<?php echo e(URL::to($Def->Logo)); ?>" alt="Logo" />
                                       
                    <?php else: ?>  
 <img style="height: 50px; width: 150px;" class="img-fluid" src="https://klarerp.com/site/img/theme/logo.png" alt="Logo" />
                      <?php endif; ?>
               
                                       </div>
                                            </div>
                                        </div>
                                        <hr>
                                        </div>
                                        </div>
                                        </div>
                    <div class="row ">
                        <div class="col-lg-12">
                            <div id="panel-2" class="panel">
                                <div class="panel-hdr no-print">
                                    <h2>
                                        <span class="fw-300"><i>   <?php echo e(trans('admin.Vendor_Account_Statement')); ?>  </i></span>
                                    </h2>
                                </div>
                                <div class="panel-container show">
                                    <div class="panel-content">
                                             <form action="<?php echo e(url('VendorAccountStatementFilterTwo')); ?>" method="get">
    
    
                                        <div class="form-row">
                                            <div class="form-group col-md-2 col-4-print">
                                                <label class="form-label" for="simpleinput"><?php echo e(trans('admin.From')); ?></label>
                                                <input type="date" id="from" name="from"  value="<?php echo e(date('Y-m-d')); ?>" class="form-control">
                                            </div>
                                            <div class="form-group col-md-2 col-4-print">
                                                <label class="form-label" for="simpleinput"><?php echo e(trans('admin.To')); ?></label>
                                                <input type="date" id="to" name="to" value="<?php echo e(date('Y-m-d')); ?>"  class="form-control">
                                            </div>
                                                 <?php if(auth()->guard('admin')->user()->vend == 0): ?> 
                                            <div class="form-group col-md-2 col-4-print">
                                                <label class="form-label" for="">   <?php echo e(trans('admin.Account')); ?></label>
                                                <select class="select2 form-control w-100" id="account" name="account">
                                             <option value=""><?php echo e(trans('admin.Account')); ?></option>        
                                                </select>
                                            </div>
                                            <?php else: ?>
                                         <input type="hidden" id="account" value="<?php echo e(auth()->guard('admin')->user()->account); ?>" name="account">      
                                            <?php endif; ?>
                                                                       <div class="form-group col-lg-3">
                                 <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Account_Credit')); ?> </label>
                             <input type="text" id="AccountCredit" value="0" class="form-control animated-btn" disabled>
                                        </div>   

                                            
                                             
                                            
                                <div class="form-group col-md-3 no-print">
                                                <label class="form-label" for="">  <?php echo e(trans('admin.Bond_Type')); ?> </label>
                                                <select class="select2 form-control w-100" id="type" name="type">
                                                                <option value=""> <?php echo e(trans('admin.Bond_Type')); ?></option>
                        <option value="القيود اليومية"> <?php echo e(trans('admin.Journalizing')); ?></option>
                   <option value="سند قبض"> <?php echo e(trans('admin.Receipt_Voucher')); ?></option>
                   <option value="سند صرف"> <?php echo e(trans('admin.Payment_Voucher')); ?></option>
                   <option value="القيد الإفتتاحي"> <?php echo e(trans('admin.Opening_Entries')); ?></option>
                   <option value="الشيكات الصادرة"> <?php echo e(trans('admin.Exporting_Checks')); ?></option>
                   <option value="الشيكات الواردة"> <?php echo e(trans('admin.Incoming_checks')); ?></option>
                 <option value="اصناف بداية فترة"> <?php echo e(trans('admin.Start_Period_Products')); ?></option>
                   <option value="تسوية بالعجز"> <?php echo e(trans('admin.Dificit_Settlement')); ?></option>
                   <option value="تسوية بالزيادة"> <?php echo e(trans('admin.Execess_Settlement')); ?></option>
                   <option value="تحويلات الخزائن"> <?php echo e(trans('admin.Safes_Transfer')); ?></option>
                   <option value="تحويلات المخازن"> <?php echo e(trans('admin.Stores_Transfers')); ?></option>
                   <option value="المشتريات"> <?php echo e(trans('admin.Purchases')); ?></option>
                   <option value="مرتجع المشتريات"> <?php echo e(trans('admin.Return_Purchases')); ?></option>
                   <option value="المبيعات"> <?php echo e(trans('admin.Sales')); ?></option>
                   <option value="مرتجع مبيعات"> <?php echo e(trans('admin.Return_Sales')); ?></option>
                   <option value="سلفة موظف"> <?php echo e(trans('admin.Emp_Borrow')); ?></option>
                   <option value="قرض موظف"> <?php echo e(trans('admin.Emp_Loan')); ?></option>
                   <option value="صرف راتب"> <?php echo e(trans('admin.AddSalary')); ?></option>
                   <option value="وصل أمانة"> <?php echo e(trans('admin.Insurance_Paper')); ?></option>
                   <option value="استلام وصل أمانة"> <?php echo e(trans('admin.Insurance_Paper_Recived')); ?></option>
                   <option value="مصاريف الأصول"> <?php echo e(trans('admin.AssetExpenses')); ?></option>
                   <option value="صرف بضاعة"> <?php echo e(trans('admin.Exchange_Goods')); ?></option>
                   <option value="استلام بضاعة"> <?php echo e(trans('admin.Recived_Goods')); ?></option>
                   <option value="صرف ارباح"> <?php echo e(trans('admin.Spend_Profits')); ?></option>
                   <option value="بيع اصل"> <?php echo e(trans('admin.Asset_Sale')); ?></option>
                   <option value="شراء أصل"> <?php echo e(trans('admin.Purchases_Asset')); ?></option>
                   <option value="الصيانة"> <?php echo e(trans('admin.Maintenance')); ?></option>
                   <option value="مرتجع فاتورة الصيانة"> <?php echo e(trans('admin.ReturnMaintainceBill')); ?></option>
                   <option value="صرف عمولات"> <?php echo e(trans('admin.ExchangeCommissions')); ?></option>
                   <option value="التصنيع"> <?php echo e(trans('admin.Manufacturing')); ?></option>
                   <option value="اشتراكات"> <?php echo e(trans('admin.SalesSubscribes')); ?></option>
                   <option value="الهالك"> <?php echo e(trans('admin.Depreciation')); ?></option>
                   <option value="البوليصه"> <?php echo e(trans('admin.')); ?></option>
                   <option value="مبيعات وقود"> <?php echo e(trans('admin.SalesPetrol')); ?></option>
                   <option value="دفع شيك وارد"> <?php echo e(trans('admin.PayIncomChecks')); ?></option>
                   <option value="رفض الشيكات الواردة"> <?php echo e(trans('admin.RefuseIncomChecks')); ?></option>
                   <option value="رفض الشيكات الصادرة"> <?php echo e(trans('admin.RefuseExportChecks')); ?></option>
                   <option value="دفع شيك صادر"> <?php echo e(trans('admin.PayExportChecks')); ?></option>   
                                                </select>
                                            </div>
                                        
                                                                                   
                                               <?php if(auth()->guard('admin')->user()->vend == 0): ?> 
                                            <div class="form-group col-md-2 no-print">
                                                <label class="form-label" for="">  <?php echo e(trans('admin.Coin')); ?></label>
                                        <select id="coin"  data-placeholder="<?php echo e(trans('admin.Coin')); ?>" name="coin" class="js-data-example-ajax form-control">
                                <option value=""><?php echo e(trans('admin.Coin')); ?></option>                
                                                </select>
                                            </div>
                                            <?php else: ?>
                                                   <input type="hidden" id="coin" name="coin" value="">  
                                            <?php endif; ?>
                                        
                                        
                                            
                                           <?php if(auth()->guard('admin')->user()->vend == 0): ?>         
                                            <div class="form-group col-md-2 no-print">
                                                <label class="form-label" for="">   <?php echo e(trans('admin.Cost_Center')); ?></label>
                             <select id="cost" data-placeholder="<?php echo e(trans('admin.Cost_Center')); ?>" class="js-data-example-ajax form-control" name="cost">  
                                 <option value=""><?php echo e(trans('admin.Cost_Center')); ?></option>  </select>
                                            </div>
                                            <div class="form-group col-md-2 no-print">
                                                <label class="form-label" for="">  <?php echo e(trans('admin.User')); ?></label>
                           <select id="user" data-placeholder="<?php echo e(trans('admin.User')); ?>" class="js-data-example-ajax form-control" name="user">    <option value=""><?php echo e(trans('admin.User')); ?></option>  </select>
                                            </div>
                                            <?php else: ?>
                                                       <input type="hidden" id="cost" value="" name="cost">        
                                                   <input type="hidden" id="user" value="" name="user">     
                                            <?php endif; ?>
                                            
                                          
                                    <!--        
                                            <div class="form-group col-md-12 no-print">
                            
                                        <div class="frame-wrap " style="margin-top: 2rem;">
                                            <div class="custom-control custom-checkbox custom-control-inline">
                         <input type="checkbox" class="custom-control-input" id="checkbox1" onclick="CheckAll()">
                                                <label class="custom-control-label" for="checkbox1"><?php echo e(trans('admin.Checked_All')); ?></label>
                                            </div>
                                           
                                        </div>
                                        </div>
                                            
                                        <div class="form-group col-md-2 no-print">
                                            <div class="frame-wrap">
                                                <div class="custom-control custom-checkbox custom-control-inline">
                   <input type="checkbox" class="custom-control-input" id="Check1" value="<?php echo e(trans('admin.Journalizing')); ?>">
                      <label class="custom-control-label" for="Check1"><?php echo e(trans('admin.Journalizing')); ?></label>
                                                </div>
                                            </div>
                                            </div>
                                      
                                           <div class="form-group col-md-2 no-print">
                                            <div class="frame-wrap">
                                                <div class="custom-control custom-checkbox custom-control-inline">
                   <input type="checkbox" class="custom-control-input" id="Check2" value="<?php echo e(trans('admin.Receipt_Voucher')); ?>">
                      <label class="custom-control-label" for="Check2"><?php echo e(trans('admin.Receipt_Voucher')); ?></label>
                                                </div>
                                            </div>
                                            </div>    
                                            
                                              <div class="form-group col-md-2 no-print">
                                            <div class="frame-wrap">
                                                <div class="custom-control custom-checkbox custom-control-inline">
                   <input type="checkbox" class="custom-control-input" id="Check3" value="<?php echo e(trans('admin.Payment_Voucher')); ?>">
                      <label class="custom-control-label" for="Check3"><?php echo e(trans('admin.Payment_Voucher')); ?></label>
                                                </div>
                                            </div>
                                            </div> 
                                            
                                                 <div class="form-group col-md-2 no-print">
                                            <div class="frame-wrap">
                                                <div class="custom-control custom-checkbox custom-control-inline">
                   <input type="checkbox" class="custom-control-input" id="Check4" value="<?php echo e(trans('admin.Opening_Entries')); ?>">
                      <label class="custom-control-label" for="Check4"><?php echo e(trans('admin.Opening_Entries')); ?></label>
                                                </div>
                                            </div>
                                            </div>
                                      
                                           <div class="form-group col-md-2 no-print">
                                            <div class="frame-wrap">
                                                <div class="custom-control custom-checkbox custom-control-inline">
                   <input type="checkbox" class="custom-control-input" id="Check5" value="<?php echo e(trans('admin.Exporting_Checks')); ?>">
                      <label class="custom-control-label" for="Check5"><?php echo e(trans('admin.Exporting_Checks')); ?></label>
                                                </div>
                                            </div>
                                            </div>    
                                            
                                              <div class="form-group col-md-2 no-print">
                                            <div class="frame-wrap">
                                                <div class="custom-control custom-checkbox custom-control-inline">
                   <input type="checkbox" class="custom-control-input" id="Check6" value="<?php echo e(trans('admin.Incoming_checks')); ?>">
                      <label class="custom-control-label" for="Check6"><?php echo e(trans('admin.Incoming_checks')); ?></label>
                                                </div>
                                            </div>
                                            </div> 
                                            
                                            -->
                                            
                                                   
                                            
                                            <div class="form-group col-md-2 no-print" style="    display: flex;margin-top: 16px;">
                                                <div class="buttons mt-2" style="display: none">
                                                    <a class="btn btn-primary" href="#" onclick="window.print()"> <i class="fal fa-print"></i> <?php echo e(trans('admin.Print')); ?></a>
                                                </div>
                                                   <div class="buttons m-2" >
                                            <button type="submit" class="btn btn-primary"><i class="fal fa-search"></i></button>    
                                                </div>
                                            </div>
                                        </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row hide-table" style="display: none">
                        <div class="col-xl-12">
                            <div id="panel-1" class="panel">
                                <div class="panel-hdr no-print">
                                    <h2>
                                        <?php echo e(trans('admin.Vendor_Account_Statement')); ?>

                                    </h2>

                                    <div class="panel-toolbar">
                                        <button class="btn btn-primary btn-sm" data-toggle="dropdown">Table
                                            Style</button>
                                        <div
                                            class="dropdown-menu dropdown-menu-animated dropdown-menu-right position-absolute pos-top">
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-bordered" data-target="#dt-basic-example"> Bordered
                                                Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-sm"
                                                data-target="#dt-basic-example"> Smaller Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-dark"
                                                data-target="#dt-basic-example"> Table Dark </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-hover" data-target="#dt-basic-example"> Table Hover
                                            </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-stripe" data-target="#dt-basic-example"> Table
                                                Stripped </button>
                                            <div class="dropdown-divider m-0"></div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    tbody color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-tbody-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    thead color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-thead-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="panel-container show">
                                    <div class="panel-content">
                                        
                                        <!-- datatable start -->
                                        <div class="over-flow">
                                        <table id="X"
                                            class="table table-bordered table-hover table-striped table-width th-td-width">
                                            <thead class="bg-highlight">
                                                <tr>
                                                    <th>   <?php echo e(trans('admin.Date')); ?></th>
                                                    <th>   <?php echo e(trans('admin.Code')); ?></th>
                                                    <th>   <?php echo e(trans('admin.Bond_Code')); ?></th>
                                                    <th>   <?php echo e(trans('admin.Bond_Type')); ?></th>
                                                    <th class="no-print">   <?php echo e(trans('admin.Statement')); ?></th>
                                                    <th>   <?php echo e(trans('admin.Coin_Debitor')); ?></th>
                                                    <th>   <?php echo e(trans('admin.Coin_Creditor')); ?></th>
                                                    <th>   <?php echo e(trans('admin.Debitor_Balance')); ?></th>
                                                    <th>   <?php echo e(trans('admin.Creditor_Balance')); ?></th>
                                                    <th class="no-print">   <?php echo e(trans('admin.Coin')); ?></th>
                                                    <th class="no-print">   <?php echo e(trans('admin.Cost_Center')); ?></th>
                                                    <th class="no-print">   <?php echo e(trans('admin.User')); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody class="Data">
             
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <th>   <?php echo e(trans('admin.Date')); ?></th>
                                                    <th>   <?php echo e(trans('admin.Code')); ?></th>
                                                    <th>   <?php echo e(trans('admin.Bond_Code')); ?></th>
                                                    <th>   <?php echo e(trans('admin.Bond_Type')); ?></th>
                                                    <th class="no-print">   <?php echo e(trans('admin.Statement')); ?></th>
                                                    <th>   <?php echo e(trans('admin.Coin_Debitor')); ?></th>
                                                    <th>   <?php echo e(trans('admin.Coin_Creditor')); ?></th>
                                                    <th>   <?php echo e(trans('admin.Debitor_Balance')); ?></th>
                                                    <th>   <?php echo e(trans('admin.Creditor_Balance')); ?></th>
                                                            <th>   <?php echo e(trans('admin.Coin')); ?></th>
                                                    <th class="no-print">   <?php echo e(trans('admin.Cost_Center')); ?></th>
                                                    <th class="no-print">   <?php echo e(trans('admin.User')); ?></th>
                                                </tr>
                                            </tfoot>
                        
                                        </table>
                                        </div>
                                        <div id="mobile-overflow">
                                        <table class="table table-bordered table-hover table-striped w-100 mt-4 mobile-width th-td-width">
                                            <tbody>
                                                <tr>
                                                    <td><?php echo e(trans('admin.Numbers')); ?></td>
                                                    <td class="NUM"></td>
                                                    <td><?php echo e(trans('admin.Total_Debitor')); ?></td>
                                                    <td class="TD"></td>
                                                    <td><?php echo e(trans('admin.Total_Creditor')); ?></td>
                                                    <td class="TC"></td>
                                                    <td><?php echo e(trans('admin.Total_Debitor_Balance')); ?></td>
                                                    <td class="TDB"></td>
                                                    <td> <?php echo e(trans('admin.Total_Creditor_Balance')); ?></td>
                                                    <td class="TCB"></td>
                                                    


                                                </tr>
                                            </tbody>
                                        </table>
                                        </div>
                                        <!-- datatable end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>




<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>

  <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">


 <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/summernote/summernote.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
    <script>
        //_fnFeatureHtmlLength();
        $(document).ready(function () {
            // Setup - add a text input to each footer cell
            $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
            $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
                var title = $(this).text();
                $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search '  + '" />');

                $('input', this).on('keyup change', function () {
                    if (table.column(i).search() !== this.value) {
                        table
                            .column(i)
                            .search(this.value)
                            .draw();
                    }
                });
            });
            var table = $('#dt-basic-example').DataTable(
                {
                    // responsive: true,
                    orderCellsTop: true,
                    fixedHeader: true,
                    lengthChange: true,

                    dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

                    buttons: [
                        {
                            extend: 'pageLength',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'colvis',
                            text: 'Column Visibility',
                            titleAttr: 'Col visibility',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            titleAttr: 'Generate PDF',
                            className: 'btn-outline-danger btn-sm mr-1'
                        },
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            titleAttr: 'Generate Excel',
                            className: 'btn-outline-success btn-sm mr-1'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            titleAttr: 'Generate CSV',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'copyHtml5',
                            text: 'Copy',
                            titleAttr: 'Copy to clipboard',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            titleAttr: 'Print Table',
                            className: 'btn-outline-primary btn-sm'
                        }
                    ],
                });
            $('.js-thead-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
            });

            $('.js-tbody-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
            });

        });

    </script>
  
 
<script>
    var autoSave = $('#autoSave');
    var interval;
    var timer = function()
    {
        interval = setInterval(function()
        {
            //start slide...
            if (autoSave.prop('checked'))
                saveToLocal();

            clearInterval(interval);
        }, 3000);
    };

    //save
    var saveToLocal = function()
    {
        localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
        console.log("saved");
    }

    //delete 
    var removeFromLocal = function()
    {
        localStorage.removeItem("summernoteData");
        $('#saveToLocal').summernote('reset');
    }

    $(document).ready(function()
    {
        //init default
        $('.js-summernote').summernote(
        {
            height: 200,
            tabsize: 2,
            placeholder: "Type here...",
            dialogsFade: true,
            toolbar: [
                ['style', ['style']],
                ['font', ['strikethrough', 'superscript', 'subscript']],
                ['font', ['bold', 'italic', 'underline', 'clear']],
                ['fontsize', ['fontsize']],
                ['fontname', ['fontname']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['height', ['height']]
                ['table', ['table']],
                ['insert', ['link', 'picture', 'video']],
                ['view', ['fullscreen', 'codeview', 'help']]
            ],
            callbacks:
            {
                //restore from localStorage
                onInit: function(e)
                {
                    $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
                },
                onChange: function(contents, $editable)
                {
                    clearInterval(interval);
                    timer();
                }
            }
        });

        //load emojis
        $.ajax(
        {
            url: 'https://api.github.com/emojis',
            async: false
        }).then(function(data)
        {
            window.emojis = Object.keys(data);
            window.emojiUrls = data;
        });

        //init emoji example
        $(".js-hint2emoji").summernote(
        {
            height: 100,
            toolbar: false,
            placeholder: 'type starting with : and any alphabet',
            hint:
            {
                match: /:([\-+\w]+)$/,
                search: function(keyword, callback)
                {
                    callback($.grep(emojis, function(item)
                    {
                        return item.indexOf(keyword) === 0;
                    }));
                },
                template: function(item)
                {
                    var content = emojiUrls[item];
                    return '<img src="' + content + '" width="20" /> :' + item + ':';
                },
                content: function(item)
                {
                    var url = emojiUrls[item];
                    if (url)
                    {
                        return $('<img />').attr('src', url).css('width', 20)[0];
                    }
                    return '';
                }
            }
        });

        //init mentions example
        $(".js-hint2mention").summernote(
        {
            height: 100,
            toolbar: false,
            placeholder: "type starting with @",
            hint:
            {
                mentions: ['jayden', 'sam', 'alvin', 'david'],
                match: /\B@(\w*)$/,
                search: function(keyword, callback)
                {
                    callback($.grep(this.mentions, function(item)
                    {
                        return item.indexOf(keyword) == 0;
                    }));
                },
                content: function(item)
                {
                    return '@' + item;
                }
            }
        });

    });

</script>
<script type="text/javascript">


    $(".show-table").click(function(){
        $(".hide-table").show();
    });
    
</script>

<script>
    function CheckAll() {  
                             if($("#checkbox1").prop("checked"))
                                {
                                    $(".custom-control-input").prop("checked",true);
                                   
                                    
                                }else 
                                if($("#checkbox1").prop("checked",false))    
                                {
                                 
                                    $(".custom-control-input").prop("checked",false);
                                  
                                    
                                }       
                                   }
                    </script>     
 
<!-- Search Selecet -->
 <script>
        $(document).ready(function()
        {
            $(function()
            {
                $('.select2').select2();

                $(".select2-placeholder-multiple").select2(
                {
                    placeholder: "Select State"
                });
                $(".js-hide-search").select2(
                {
                    minimumResultsForSearch: 1 / 0
                });
                $(".js-max-length").select2(
                {
                    maximumSelectionLength: 2,
                    placeholder: "Select maximum 2 items"
                });
                $(".select2-placeholder").select2(
                {
                    placeholder: "Select a state",
                    allowClear: true
                });

                $(".js-select2-icons").select2(
                {
                    minimumResultsForSearch: 1 / 0,
                    templateResult: icon,
                    templateSelection: icon,
                    escapeMarkup: function(elm)
                    {
                        return elm
                    }
                });

                function icon(elm)
                {
                    elm.element;
                    return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
                }

  
  $('#coin').select2({
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllCoins',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };            
        },
        data: function (params) {  
   
            
                   var query = {
                            search: params.term,
                        };
                                
            
              $.ajax({
                              url: 'AllCoinsJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('#coin').empty();  
                                  $.each(data, function(key, value){
   
                         $('#coin').append('<option value="'+ key +'">' + value + '</option>');
                   
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
            
            
            
        }
        }
  });

           
    
$('#coin').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});
            
        
  $('#account').select2({
    placeholder: "select...",
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllVendors',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };
            
            	console.log(data);
            
        },
          data: function (params) {  
   
            
                   var query = {
                            search: params.term,
                        };
                                
            
              $.ajax({
                              url: 'AllVendorsJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('#account').empty();  
                                  $.each(data, function(key, value){
   
                         $('#account').append('<option value="'+ key +'">' + value + '</option>');
                   
                                  });
                                  
                                   var countryId = $('#account').val();
                                            $.ajax({
                              url: 'AccountBalanceSOFilter/'+countryId,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                                  $.each(data, function(key, value){
   
        
                    $('#AccountCredit').val(key); 
                    $('#AccountCredit').val(value); 
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });  
                                  
           
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
            
            
            
        }
    }
  });

    
$('#account').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});
           
                
      $('#cost').select2({
    placeholder: "select...",
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllCostss',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };
            
            	console.log(data);
            
        },
    
         data: function (params) {  
   
            
                   var query = {
                            search: params.term,
                        };
                                
            
              $.ajax({
                              url: 'AllCostssJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('#cost').empty();  
                                  $.each(data, function(key, value){
   
                         $('#cost').append('<option value="'+ key +'">' + value + '</option>');
                   
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
            
            
            
        }
        
    }
  });

    
$('#cost').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});
      
                
                
  $('#user').select2({
    placeholder: "select...",
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllUsers',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };
            
            	console.log(data);
            
        },
         data: function (params) {  
   
            
                   var query = {
                            search: params.term,
                        };
                                
            
              $.ajax({
                              url: 'AllUsersJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('#user').empty();  
                                  $.each(data, function(key, value){
   
                         $('#user').append('<option value="'+ key +'">' + value + '</option>');
                   
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
            
            
            
        }
    }
  });

    
$('#user').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});
                      

             
            });
        });



    </script>

<!-- Account Balance -->
<script>
   $(document).ready(function() {
   
                  $('#account').on('change', function(){
                      var countryId = $(this).val();
                      if(countryId) {
                          $.ajax({
                              url: 'AccountBalanceSOFilter/'+countryId,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                                  $.each(data, function(key, value){
   
        
                    $('#AccountCredit').val(key); 
                    $('#AccountCredit').val(value); 
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {
   
                          $('select[name="state"]').empty();
                      }
   
                  });
   
              });
    
     $(document).ready(function() { 
 
                      var countryId = $('#account').val();
                      if(countryId) {
                          $.ajax({
                              url: 'AccountBalanceSFilter/'+countryId,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                                  $.each(data, function(key, value){
   
        
                    $('#AccountCredit').val(key); 
                    $('#AccountCredit').val(value); 
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {
   
                          $('select[name="state"]').empty();
                      }
     
     });
</script>  



<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\erp\resources\views/admin/AccountsReports/Vendor_Account_Statement.blade.php ENDPATH**/ ?>
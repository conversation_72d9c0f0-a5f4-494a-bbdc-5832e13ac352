<?php $__env->startSection('content'); ?>
<?php
use App\Models\AcccountingManual;
$AEryd=AcccountingManual::where('id',15)->first();
$ATaklfa=AcccountingManual::where('id',16)->first();
$AMsrfoat=AcccountingManual::where('id',17)->first();
?>
<?php
use App\Models\CompanyData;
$Def=CompanyData::orderBy('id','desc')->first();
?>


  <title><?php echo e(trans('admin.Financial_Center')); ?></title>


    <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb no-print">
                      <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Accounts_Reports')); ?> </a></li>
                        <li class="breadcrumb-item active">  <?php echo e(trans('admin.Financial_Center')); ?>   </li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                class="js-get-date"></span></li>
                    </ol>
                    
        
              
                               <!-- Head Of Print -->
                     <div class="row Headerrr">
                      <div class="col-4">
                            <div id="panel-1" class="panel d-none d-print-block" style="margin-top:30px;">
                      <div class="row invoive-info ">
                       
                                          <div  style="text-align: center;" class="col-md-4 col-sm-4 col-4 invoice-client-info">
                                                <div class="style-info">
                                                    <h1 class="HeaderInvoice" style="text-align: center;font-size:15px" class="m-0">
                                               <?php if(!empty($Def->Name)): ?>
                      <?php echo e($Def->Name); ?>

                      <?php else: ?>
                       <?php echo e(trans('admin.Ost')); ?>

                      <?php endif; ?>         
                                                    </h1>
                              <h3  style="text-align: center;font-size:15px" class=" m-10">
                         <?php if(!empty($Def->Print_Text)): ?>
                      <?php echo e($Def->Print_Text); ?>

                      <?php endif; ?>                
                                                    </h3>
                                                </div>
                                            </div>
                                        
                                        </div>
                                        <hr>
                                        </div>
                                        </div>  
                         
                              <div class="col-4 text-center" >
                            <div id="panel-1" class="panel d-none d-print-block" style="margin-top:30px;">
                      <div class="row invoive-info " style="display:flex;  justify-content: center;">
                                          
                                            <div style="text-align: center;" class="col-md-5 col-sm-5 col-5 invoice-client-info" style="border:unset;">
                                                <h1 style="text-align: center;" class="HeaderInvoice"> <?php echo e(trans('admin.Vendor_Account_Statement')); ?> </h1>
                                            </div>
                                 
                                        </div>
                                        <hr>
                                        </div>
                                        </div>
                                        
                                <div class="col-4">
                            <div id="panel-1" class="panel d-none d-print-block" style="margin-top:30px;">
                      <div class="row invoive-info "style="display:flex;         justify-content: flex-end;">
                                         
                                   <div  style="text-align: center; " class="col-md-3 col-sm-3 col-3 invoice-client-info">
                         <div class="style-info">
                       <?php if(!empty($Def->Logo)): ?>         
    <img class="logoPri" style="height: 50px; " class="img-fluid" src="<?php echo e(URL::to($Def->Logo)); ?>" alt="Logo" />
                                       
                    <?php else: ?>  
 <img class="logoPri" style="height: 50px; width: 150px;" class="img-fluid" src="https://klarerp.com/site/img/theme/logo.png" alt="Logo" />
                      <?php endif; ?>
               
                                       </div>
                                            </div>
                         
                    
                                        </div>
                                        <hr>
                                        </div>
                                        </div>            
                                        
                                        </div>
       
                    <!-- data entry -->
                    <div class="row ">
                        <div class="col-lg-12">
                            <div id="panel-2" class="panel">
                                <div class="panel-hdr no-print">
                                    <h2>
                                        <span class="fw-300"><i>  <?php echo e(trans('admin.Financial_Center')); ?>     </i></span>
                                    </h2>
                                </div>
                                <div class="panel-container show">
                                    <div class="panel-content">
                                        <form class="form-row">
                                            <div class="form-group col-md-3 col-4" style="display: none">
                                              <label class="form-label" for="simpleinput"><?php echo e(trans('admin.From')); ?></label>
                                                <input type="date" id="from"  class="form-control">
                                            </div>
                                            <div class="form-group col-md-3 col-4">
                                           <label class="form-label" for="simpleinput"><?php echo e(trans('admin.To')); ?></label>
                                                <input type="date" id="to"  class="form-control">
                                            </div>
                                            <div class="form-group col-md-3 no-print" style="display: none">
                                     <label class="form-label" for="">  <?php echo e(trans('admin.Coin')); ?></label>
              <select id="coin"  data-placeholder="<?php echo e(trans('admin.Coin')); ?>" class="select2 form-control">
                                <option value="" selected><?php echo e(trans('admin.Coin')); ?></option>                
                                                </select>
                                            </div>
                                            <div class="form-group col-md-3 col-4">
                              <label class="form-label" for="">  <?php echo e(trans('admin.Account')); ?></label>
                                                <select class="select2 form-control w-100" id="type">
                                                    <option value="0"><?php echo e(trans('admin.Main')); ?></option>
                                                    <option value="1"><?php echo e(trans('admin.Sub')); ?></option>
                                                </select>
                                            </div>
                                            <div class="form-group col-md-3 no-print">
                                                <div class="buttons mt-4">
                                <a class=" btn btn-primary w-100" href="#" onclick="ModalNoPrint()"> <i class="fal fa-print"></i> <?php echo e(trans('admin.Print')); ?></a>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row ">
                        <div class="col-xl-12">
                            <div id="panel-1" class="panel">
                                <div class="panel-hdr">
                                    <h2>
                                   <?php echo e(trans('admin.Financial_Center')); ?>

                                    </h2>

                                </div>
                                <div class="panel-container show">
                                    <div class="panel-content">
 
                                        <!-- datatable start -->
                                        <table id="X" class="table table-bordered">
                                            <thead class="bg-highlight">
                                                <tr>
                                                    <th><?php echo e(trans('admin.Account_Name')); ?></th>
                                                    <th><?php echo e(trans('admin.Coin_Debitor')); ?></th>
                                                    <th><?php echo e(trans('admin.Coin_Creditor')); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody class="Data">
                                    
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <th><?php echo e(trans('admin.Account_Name')); ?></th>
                                                    <th><?php echo e(trans('admin.Coin_Debitor')); ?></th>
                                                    <th><?php echo e(trans('admin.Coin_Creditor')); ?></th>
                                                </tr>
                                            </tfoot>
                        
                                        </table>
                                     
                                        <!-- datatable end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>


<!-- Modal  Asoul Details-->
<div class="modal fade" id="AsoulDet" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel"><?php echo e($AEryd->Name); ?></h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
          
          
          
                              <table  class="table table-bordered">
                                            <thead class="bg-highlight">
                                                <tr>
                                                    <th><?php echo e(trans('admin.Account_Name')); ?></th>
                                                    <th><?php echo e(trans('admin.Coin_Debitor')); ?></th>
                                                    <th><?php echo e(trans('admin.Coin_Creditor')); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody class="DataAsoul">
                                    
                                            </tbody>
                                        </table>
      </div>
      <div class="modal-footer">
             <button type="button" class="btn btn-primary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
        <button type="button" onclick="ModalPrint()" class="btn btn-primary"><?php echo e(trans('admin.Print')); ?></button>
      </div>
    </div>
  </div>
</div>


<!-- Modal  Khsoum Details-->
<div class="modal fade" id="KhsoumDet" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel"><?php echo e($ATaklfa->Name); ?></h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
                         <table  class="table table-bordered">
                                            <thead class="bg-highlight">
                                                <tr>
                                                    <th><?php echo e(trans('admin.Account_Name')); ?></th>
                                                    <th><?php echo e(trans('admin.Coin_Debitor')); ?></th>
                                                    <th><?php echo e(trans('admin.Coin_Creditor')); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody class="DataKhsoum">
                                    
                                            </tbody>
                                        </table>
      </div>
      <div class="modal-footer">
             <button type="button" class="btn btn-primary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
        <button type="button" onclick="ModalPrint()" class="btn btn-primary"><?php echo e(trans('admin.Print')); ?></button>
      </div>
    </div>
  </div>
</div>


<!-- Modal  Hkook Details-->
<div class="modal fade" id="HkookDet" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel"><?php echo e($AMsrfoat->Name); ?></h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
                    <table  class="table table-bordered">
                                            <thead class="bg-highlight">
                                                <tr>
                                                    <th><?php echo e(trans('admin.Account_Name')); ?></th>
                                                    <th><?php echo e(trans('admin.Coin_Debitor')); ?></th>
                                                    <th><?php echo e(trans('admin.Coin_Creditor')); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody class="DataHkook">
                                    
                                            </tbody>
                                        </table>
      </div>
      <div class="modal-footer">
             <button type="button" class="btn btn-primary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
        <button type="button" onclick="ModalPrint()" class="btn btn-primary"><?php echo e(trans('admin.Print')); ?></button>
      </div>
    </div>
  </div>
</div>


<?php $__env->stopSection(); ?>


<?php $__env->startPush('js'); ?>

<!-- Colors  Sub -->
<style>
    .Asoul{
        
    background: linear-gradient(45deg, black, #B30347);
        color: white;
        
    }
    .Khsoum{
          background: linear-gradient(45deg, black, #e4b708);
        color: white;  
        
    }
    .Hkook{
           background: linear-gradient(45deg, black, #13a76a);
        color: white;   
        
    }
    .DIFFERENECE{
          background: linear-gradient(45deg, #554b7f, #ffffff);
        color: black;   
        
    }
    
    
</style>

<!-- Colors  Main -->
<style>

    .AlAsoul{
          background: linear-gradient(45deg, #B30347, #ffffff);
        color: black;   
        
    }
    
       .AlAsoulSabta{
          background: linear-gradient(45deg, #630479, #ffffff);
        color: black;   
        
    }
    
       .AlAsoulMtdawla{
          background: linear-gradient(45deg, #272727, #ffffff);
        color: black;   
        
    }
    
    .Khsoum{
          background: linear-gradient(45deg, black, #e4b708);
        color: black;  
        
    }
    
       .gray{
          background: linear-gradient(45deg, #078593, #ffffff);
        color: black;   
        
    }
       .HkokMlkya{
          background: linear-gradient(45deg, #a37439, #ffffff);
        color: black;   
        
    }
       .SafyRabh{
          background: linear-gradient(45deg, #f3c409, #ffffff);
        color: black;   
        
    }
       .Fark{
          background: linear-gradient(45deg, #C10001, #ffffff);
        color: black;   
        
    }
    
          .AlAsoulOkhra{
          background: linear-gradient(45deg, #554b7f, #ffffff);
        color: black;   
        
    }
    
    
</style>    

<!-- Filter -->
<script>
   $(document).ready(function(){
   
    fetch_customer_data();
   
    function fetch_customer_data(From = '',To = '',Type = '',Coin = '')
    {
     $.ajax({
      url:'FilterFinancial_CenterNew',
      method:'GET',
      data:{From:From,To:To,Type:Type,Coin:Coin},
      dataType:'json',
      success:function(data)
      {

       $('.Data').html(data.table_data);     
   
      }
     })
    }
    

  $(document).on('change', '#from', function(){
     var From = $(this).val();     
     var To = $('#to').val();     
     var Type = $('#type').val();     
     var Coin = $('#coin').val();      
  fetch_customer_data(From,To,Type,Coin);
    });   

  $(document).on('change', '#to', function(){
     var To = $(this).val();     
     var From = $('#from').val();     
     var Type = $('#type').val();     
     var Coin = $('#coin').val();      
  fetch_customer_data(From,To,Type,Coin);
    });   
       
  $(document).on('change', '#type', function(){
     var Type = $(this).val();     
     var To = $('#to').val();     
     var From = $('#from').val();     
     var Coin = $('#coin').val();       
  fetch_customer_data(From,To,Type,Coin);
    });   
       
  $(document).on('change', '#coin', function(){
     var Coin = $(this).val();     
     var To = $('#to').val();     
     var Type = $('#type').val();     
     var From = $('#from').val();  
  fetch_customer_data(From,To,Type,Coin);
    });          
 
       
   });
</script>

    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">

    <style>
        th{
            width:135px!important;
        }
    </style>

 <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/summernote/summernote.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
    <script>
        //_fnFeatureHtmlLength();
        $(document).ready(function () {
            // Setup - add a text input to each footer cell
            $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
            $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
                var title = $(this).text();
                $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search ' + title + '" />');

                $('input', this).on('keyup change', function () {
                    if (table.column(i).search() !== this.value) {
                        table
                            .column(i)
                            .search(this.value)
                            .draw();
                    }
                });
            });
            var table = $('#dt-basic-example').DataTable(
                {
                    responsive: true,
                    orderCellsTop: true,
                    fixedHeader: true,
                    lengthChange: true,

                    dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

                    buttons: [
                        {
                            extend: 'pageLength',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'colvis',
                            text: 'Column Visibility',
                            titleAttr: 'Col visibility',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            titleAttr: 'Generate PDF',
                            className: 'btn-outline-danger btn-sm mr-1'
                        },
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            titleAttr: 'Generate Excel',
                            className: 'btn-outline-success btn-sm mr-1'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            titleAttr: 'Generate CSV',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'copyHtml5',
                            text: 'Copy',
                            titleAttr: 'Copy to clipboard',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            titleAttr: 'Print Table',
                            className: 'btn-outline-primary btn-sm'
                        }
                    ],
                });
            $('.js-thead-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
            });

            $('.js-tbody-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
            });

        });

    </script>
  
  <script>
    $(document).ready(function()
    {
        $(function()
        {
            $('.select2').select2();

            $(".select2-placeholder-multiple").select2(
            {
                placeholder: "Select State"
            });
            $(".js-hide-search").select2(
            {
                minimumResultsForSearch: 1 / 0
            });
            $(".js-max-length").select2(
            {
                maximumSelectionLength: 2,
                placeholder: "Select maximum 2 items"
            });
            $(".select2-placeholder").select2(
            {
                placeholder: "Select a state",
                allowClear: true
            });

            $(".js-select2-icons").select2(
            {
                minimumResultsForSearch: 1 / 0,
                templateResult: icon,
                templateSelection: icon,
                escapeMarkup: function(elm)
                {
                    return elm
                }
            });

            function icon(elm)
            {
                elm.element;
                return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
            }

            $(".js-data-example-ajax").select2(
            {
                ajax:
                {
                    url: "https://api.github.com/search/repositories",
                    dataType: 'json',
                    delay: 250,
                    data: function(params)
                    {
                        return {
                            q: params.term, // search term
                            page: params.page
                        };
                    },
                    processResults: function(data, params)
                    {
                        // parse the results into the format expected by Select2
                        // since we are using custom formatting functions we do not need to
                        // alter the remote JSON data, except to indicate that infinite
                        // scrolling can be used
                        params.page = params.page || 1;

                        return {
                            results: data.items,
                            pagination:
                            {
                                more: (params.page * 30) < data.total_count
                            }
                        };
                    },
                    cache: true
                },
                placeholder: 'Search for a repository',
                escapeMarkup: function(markup)
                {
                    return markup;
                }, // let our custom formatter work
                minimumInputLength: 1,
                templateResult: formatRepo,
                templateSelection: formatRepoSelection
            });

            function formatRepo(repo)
            {
                if (repo.loading)
                {
                    return repo.text;
                }

                var markup = "<div class='select2-result-repository clearfix d-flex'>" +
                    "<div class='select2-result-repository__avatar mr-2'><img src='" + repo.owner.avatar_url + "' class='width-2 height-2 mt-1 rounded' /></div>" +
                    "<div class='select2-result-repository__meta'>" +
                    "<div class='select2-result-repository__title fs-lg fw-500'>" + repo.full_name + "</div>";

                if (repo.description)
                {
                    markup += "<div class='select2-result-repository__description fs-xs opacity-80 mb-1'>" + repo.description + "</div>";
                }

                markup += "<div class='select2-result-repository__statistics d-flex fs-sm'>" +
                    "<div class='select2-result-repository__forks mr-2'><i class='fal fa-lightbulb'></i> " + repo.forks_count + " Forks</div>" +
                    "<div class='select2-result-repository__stargazers mr-2'><i class='fal fa-star'></i> " + repo.stargazers_count + " Stars</div>" +
                    "<div class='select2-result-repository__watchers mr-2'><i class='fal fa-eye'></i> " + repo.watchers_count + " Watchers</div>" +
                    "</div>" +
                    "</div></div>";

                return markup;
            }

            function formatRepoSelection(repo)
            {
                return repo.full_name || repo.text;
            }
        });
    });

</script>
<script>
    var autoSave = $('#autoSave');
    var interval;
    var timer = function()
    {
        interval = setInterval(function()
        {
            //start slide...
            if (autoSave.prop('checked'))
                saveToLocal();

            clearInterval(interval);
        }, 3000);
    };

    //save
    var saveToLocal = function()
    {
        localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
        console.log("saved");
    }

    //delete 
    var removeFromLocal = function()
    {
        localStorage.removeItem("summernoteData");
        $('#saveToLocal').summernote('reset');
    }

    $(document).ready(function()
    {
        //init default
        $('.js-summernote').summernote(
        {
            height: 200,
            tabsize: 2,
            placeholder: "Type here...",
            dialogsFade: true,
            toolbar: [
                ['style', ['style']],
                ['font', ['strikethrough', 'superscript', 'subscript']],
                ['font', ['bold', 'italic', 'underline', 'clear']],
                ['fontsize', ['fontsize']],
                ['fontname', ['fontname']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['height', ['height']]
                ['table', ['table']],
                ['insert', ['link', 'picture', 'video']],
                ['view', ['fullscreen', 'codeview', 'help']]
            ],
            callbacks:
            {
                //restore from localStorage
                onInit: function(e)
                {
                    $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
                },
                onChange: function(contents, $editable)
                {
                    clearInterval(interval);
                    timer();
                }
            }
        });

        //load emojis
        $.ajax(
        {
            url: 'https://api.github.com/emojis',
            async: false
        }).then(function(data)
        {
            window.emojis = Object.keys(data);
            window.emojiUrls = data;
        });

        //init emoji example
        $(".js-hint2emoji").summernote(
        {
            height: 100,
            toolbar: false,
            placeholder: 'type starting with : and any alphabet',
            hint:
            {
                match: /:([\-+\w]+)$/,
                search: function(keyword, callback)
                {
                    callback($.grep(emojis, function(item)
                    {
                        return item.indexOf(keyword) === 0;
                    }));
                },
                template: function(item)
                {
                    var content = emojiUrls[item];
                    return '<img src="' + content + '" width="20" /> :' + item + ':';
                },
                content: function(item)
                {
                    var url = emojiUrls[item];
                    if (url)
                    {
                        return $('<img />').attr('src', url).css('width', 20)[0];
                    }
                    return '';
                }
            }
        });

        //init mentions example
        $(".js-hint2mention").summernote(
        {
            height: 100,
            toolbar: false,
            placeholder: "type starting with @",
            hint:
            {
                mentions: ['jayden', 'sam', 'alvin', 'david'],
                match: /\B@(\w*)$/,
                search: function(keyword, callback)
                {
                    callback($.grep(this.mentions, function(item)
                    {
                        return item.indexOf(keyword) == 0;
                    }));
                },
                content: function(item)
                {
                    return '@' + item;
                }
            }
        });

    });

</script>
<script type="text/javascript">


    $(".show-table").click(function(){
        $(".hide-table").show();
    });
    
</script>


<!-- Search Selecet -->
 <script>
        $(document).ready(function()
        {
            $(function()
            {
                $('.select2').select2();

                $(".select2-placeholder-multiple").select2(
                {
                    placeholder: "Select State"
                });
                $(".js-hide-search").select2(
                {
                    minimumResultsForSearch: 1 / 0
                });
                $(".js-max-length").select2(
                {
                    maximumSelectionLength: 2,
                    placeholder: "Select maximum 2 items"
                });
                $(".select2-placeholder").select2(
                {
                    placeholder: "Select a state",
                    allowClear: true
                });

                $(".js-select2-icons").select2(
                {
                    minimumResultsForSearch: 1 / 0,
                    templateResult: icon,
                    templateSelection: icon,
                    escapeMarkup: function(elm)
                    {
                        return elm
                    }
                });

                function icon(elm)
                {
                    elm.element;
                    return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
                }

                 
  $('#coin').select2({
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllCoins',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };            
        },
        data: function (params) {  
     
          return {json: JSON.stringify( params.term )} 
                               
        }
        }
  });

           
    
$('#coin').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});
              
                

             
            });
        });



    </script>


<!-- Details Fiilter -->
<script>
 function DetailsAsoul(){
  
     var To = $('#to').val();     
     var Type = $('#type').val();     

     $.ajax({
      url:'FilterFinancial_CenterAsoul',
      method:'GET',
      data:{To:To,Type:Type},
      dataType:'json',
      success:function(data)
      {

       $('.DataAsoul').html(data.table_data);     
   
      }
     })
  


 }
    
     function DetailsKhsoum(){
     
         
            var To = $('#to').val();     
     var Type = $('#type').val();     

     $.ajax({
      url:'FilterFinancial_CenterKhsoum',
      method:'GET',
      data:{To:To,Type:Type},
      dataType:'json',
      success:function(data)
      {

       $('.DataKhsoum').html(data.table_data);     
   
      }
     })
     

     
 }
    
     function DetailsHkook(){
     
         
             var To = $('#to').val();     
     var Type = $('#type').val();     

     $.ajax({
      url:'FilterFinancial_CenterHkook',
      method:'GET',
      data:{To:To,Type:Type},
      dataType:'json',
      success:function(data)
      {

       $('.DataHkook').html(data.table_data);     
   
      }
     })
     
     
 }

</script>


<!-- Print Modal -->

<script>
function ModalPrint(){
    
    $('#js-page-content').addClass('no-print');
     $('#AsoulDet').removeClass('no-print');
     $('#KhsoumDet').removeClass('no-print');
     $('#HkookDet').removeClass('no-print');
    window.print();
    
}
    function ModalNoPrint(){
    $('#js-page-content').removeClass('no-print');
    $('#AsoulDet').addClass('no-print');
    $('#KhsoumDet').addClass('no-print');
    $('#HkookDet').addClass('no-print');
    window.print();
    
}
</script>
	<style>
	@media  print {

		.modal-content * {
			visibility: visible;
			overflow: visible;
		}
		.main-page * {
			display: none;
		}
		.modal {
			position: absolute;
			left: 0;
			top: 0;
			margin: 0;
			padding: 0;
			min-height: 550px;
			visibility: visible;
			overflow: visible !important; /* Remove scrollbar for printing. */
		}
		.modal-dialog {
			visibility: visible !important;
			overflow: visible !important; /* Remove scrollbar for printing. */
		}
	}
	</style>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\erp\resources\views/admin/AccountsReports/Financial_CenterNew.blade.php ENDPATH**/ ?>
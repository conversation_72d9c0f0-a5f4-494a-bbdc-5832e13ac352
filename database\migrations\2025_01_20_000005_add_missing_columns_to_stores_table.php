<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToStoresTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('stores', function (Blueprint $table) {
            // Add all the missing columns that are expected by the Stores model
            $table->string('Code')->nullable();
            $table->date('Date')->nullable();
            $table->string('Time')->nullable();
            $table->string('Phone')->nullable();
            $table->text('Address')->nullable();
            $table->string('Account')->nullable(); // Foreign key to acccounting_manuals
            $table->string('User')->nullable(); // Foreign key to admins
            $table->string('Branch')->nullable(); // Foreign key to branches
            $table->string('Letter')->nullable();
            $table->string('Account_Client')->nullable(); // Foreign key to acccounting_manuals
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('stores', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn([
                'Code', 'Date', 'Time', 'Phone', 'Address', 'Account', 'User', 'Branch', 'Letter', 'Account_Client'
            ]);
        });
    }
}

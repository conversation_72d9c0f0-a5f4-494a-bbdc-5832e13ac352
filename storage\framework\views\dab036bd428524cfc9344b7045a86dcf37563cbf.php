<?php $__env->startSection('content'); ?>
<?php
use App\Models\AccountsDefaultData;
$Def=AccountsDefaultData::orderBy('id','desc')->first();
?>
  <title><?php echo e(trans('admin.Assets')); ?></title>

              <!-- the #js-page-content id is needed for some plugins to initialize -->
                <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Accounts')); ?></a></li>
                        <li class="breadcrumb-item active"> <?php echo e(trans('admin.Assets')); ?></li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                class="js-get-date"></span></li>
                    </ol>
                                 <!-- data entry -->
            <div class="panel-container show">
                                    <div class="panel-content">


     <form action="<?php echo e(url('AssetsSechduleFilter')); ?>" method="get">

                                        <div class="form-row">
                                            <div class="form-group col-md-3 col-4-print">
                                                <label class="form-label" for="simpleinput"><?php echo e(trans('admin.From')); ?></label>
                                                <input type="date" id="from" value="<?php echo e(date('Y-m-d')); ?>" name="From" class="form-control" required>
                                            </div>
                                            <div class="form-group col-md-3 col-4-print">
                                                <label class="form-label" for="simpleinput"><?php echo e(trans('admin.To')); ?></label>
                                                <input type="date" id="to" value="<?php echo e(date('Y-m-d')); ?>" name="To" class="form-control" required>
                                            </div>
                                           <div class="form-group col-lg-3">
                                    <label class="form-label" for=""> <?php echo e(trans('admin.Main_Account')); ?> </label>
                    <select class="js-data-example-ajax form-control w-100" name="Main_Account" id="Main_New_Account" >
                                    </select>
                                </div>
                                <div class="form-group col-lg-3">
                                    <label class="form-label" for=""> <?php echo e(trans('admin.Asset_Type')); ?>  </label>
                                    <select class="select2 form-control w-100"   name="Asset_Type" >
                                        <option value=""><?php echo e(trans('admin.Asset_Type')); ?> </option>
                                        <option value="<?php echo e(trans('admin.consumer')); ?>"><?php echo e(trans('admin.consumer')); ?> </option>
                                        <option value="<?php echo e(trans('admin.unconsumed')); ?>"><?php echo e(trans('admin.unconsumed')); ?> </option>
                                    </select>
                                </div>
                                <div class="form-group col-lg-3" id="M1" style="display: none">
                                    <label class="form-label" for=""> <?php echo e(trans('admin.Depreciation_Method')); ?>  </label>
                                    <select class="select2 form-control w-100" name="Depreciation_Method" >
                                        <option value=""><?php echo e(trans('admin.Depreciation_Method')); ?> </option>
                                        <option value="<?php echo e(trans('admin.Fixed')); ?>"><?php echo e(trans('admin.Fixed')); ?> </option>
                                        <option value="<?php echo e(trans('admin.decreasing')); ?>"><?php echo e(trans('admin.decreasing')); ?> </option>
                                    </select>
                                </div>

                                            <div class="buttons m-2">
                                            <button type="submit" class="btn btn-primary"><i class="fal fa-search"></i></button>
                                                </div>
          </div>
                                        </form>
                                    </div>
                                </div>



                    <div class="row">
                        <div class="col-xl-12">
                            <div id="panel-1" class="panel">
                                <div class="panel-hdr">
                                   <h2>
                                       <?php echo e(trans('admin.Assets')); ?>

                                    </h2>

                                    <div class="panel-toolbar">

                                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه اصل')): ?>
                                         <button type="button" class="btn btn-default btn-sm margin-btn" data-toggle="modal" data-target="#default-example-modal-center-add">
                                        <?php echo e(trans('admin.AddNew')); ?></button>
                                        <?php endif; ?>

                                        <button class="btn btn-primary btn-sm" data-toggle="dropdown">Table
                                            Style</button>
                                        <div class="dropdown-menu dropdown-menu-animated dropdown-menu-right position-absolute pos-top">
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-bordered" data-target="#dt-basic-example"> Bordered
                                                Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-sm"
                                                data-target="#dt-basic-example"> Smaller Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-dark"
                                                data-target="#dt-basic-example"> Table Dark </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-hover" data-target="#dt-basic-example"> Table Hover
                                            </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-stripe" data-target="#dt-basic-example"> Table
                                                Stripped </button>
                                            <div class="dropdown-divider m-0"></div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    tbody color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-tbody-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    thead color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-thead-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="panel-container show">
                                             <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                    <div class="panel-content">

                                        <!-- datatable start -->
                                        <div  id="mobile-overflow">
                                        <table id="dt-basic-example"
                                            class="table table-bordered table-hover table-striped w-100 mobile-width" >
                                            <thead class="bg-highlight">
                                                <tr>
                                                    <th><?php echo e(trans('admin.Code')); ?></th>
                                                    <th><?php echo e(trans('admin.Arabic_Name')); ?></th>
                                                    <th><?php echo e(trans('admin.English_Name')); ?></th>
                                                    <th> <?php echo e(trans('admin.Image')); ?></th>
                                                    <th> <?php echo e(trans('admin.Account_Code')); ?></th>
                                                    <th> <?php echo e(trans('admin.Payment_Method')); ?></th>
                                                    <th> <?php echo e(trans('admin.Data')); ?></th>
                                                    <th> <?php echo e(trans('admin.Actions')); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php echo e($item->Code); ?></td>
                                                    <td><?php echo e($item->Name); ?></td>
                                                    <td><?php echo e($item->NameEn); ?></td>

                                                    <td><img src="<?php echo e(URL::to($item->Image)); ?>"></td>

                                                    <td>
                                                        <?php if($item->Account()->first()): ?>
                                                            <?php echo e($item->Account()->first()->Code); ?>

                                                        <?php else: ?>
                                                            <?php echo e(trans('admin.Not_Found')); ?>

                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                    <?php if($item->Payment_Method == 'Cash'): ?>
                                                        <?php echo e(trans('admin.Cash')); ?>

                                                    <?php elseif($item->Payment_Method == 'Later'): ?>
                                                        <?php echo e(trans('admin.Later')); ?>

                                                    <?php elseif($item->Payment_Method == 'Installment'): ?>
                                                        <?php echo e(trans('admin.Installment')); ?>

                                                    <?php elseif($item->Payment_Method == 'Check'): ?>
                                                        <?php echo e(trans('admin.Check')); ?>


                                                    <?php endif; ?>
                                                    </td>


                                                     <td><button type="button" class="btn btn-primary" data-toggle="modal" data-target="#show-data<?php echo e($item->id); ?>">
                                                         <?php echo e(trans('admin.Data')); ?>

                                                        </button>
                                                    <td>

                                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('حذف اصل')): ?>
        <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Delete<?php echo e($item->id); ?>">
                                                                        <i class="fal fa-trash"></i>
                                                        </button>

                                            <?php endif; ?>


                     <button type="button" class="btn btn-default" style="width:65px;" data-toggle="modal" data-target="#Sale<?php echo e($item->id); ?>">
                                                 <?php echo e(trans('admin.Asset_Sale')); ?>

                                                        </button>

                                                    </td>
                                                </tr>
                                              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                 <th><?php echo e(trans('admin.Code')); ?></th>
                                                    <th><?php echo e(trans('admin.Arabic_Name')); ?></th>
                                                    <th><?php echo e(trans('admin.English_Name')); ?></th>
                                                    <th> <?php echo e(trans('admin.Image')); ?></th>
                                                    <th> <?php echo e(trans('admin.Account_Code')); ?></th>
                                                    <th> <?php echo e(trans('admin.Payment_Method')); ?></th>
                                                    <th> <?php echo e(trans('admin.Data')); ?></th>
                                                    <th> <?php echo e(trans('admin.Actions')); ?></th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                       </div>
                                        <?php echo e($items->Links()); ?>

                                        <!-- datatable end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
                 <!-- Modal Add -->
                 <div class="modal fade" id="default-example-modal-center-add" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h4 class="modal-title">
                                   <?php echo e(trans('admin.AddNew')); ?>

                                </h4>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                </button>
                            </div>
                    <form action="<?php echo e(url('AddAssets')); ?>" method="post" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>

                        <?php echo view('honeypot::honeypotFormFields'); ?>
                            <div class="modal-body">

                               <div class="row">
                                <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Code')); ?></label>
                                    <input type="text"  value="<?php echo e($Code); ?>" disabled class="form-control">
                                    <input type="hidden" name="Code"  value="<?php echo e($Code); ?>">
                                </div>
                                <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Arabic_Name')); ?></label>
                                    <input type="text" name="Name" value="<?php echo e(old('Name')); ?>" class="form-control" required>
                                </div>
                                   <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.English_Name')); ?></label>
                                    <input type="text" name="NameEn" value="<?php echo e(old('NameEn')); ?>" class="form-control" >
                                </div>
                                <div class="form-group col-lg-3">
                                    <label class="form-label" for=""> <?php echo e(trans('admin.Main_Account')); ?> </label>
                    <select class="js-data-example-ajax form-control w-100" name="Main_Account" id="Main_Account" required>
                                    </select>
                                </div>
                                <div class="form-group col-lg-3">
                                    <label class="form-label" for=""> <?php echo e(trans('admin.Asset_Type')); ?>  </label>
                                    <select class="select2 form-control w-100" id="type" onchange="Appear()" name="Asset_Type" required>
                                        <option value=""><?php echo e(trans('admin.Asset_Type')); ?> </option>
                                        <option value="consumer"><?php echo e(trans('admin.consumer')); ?> </option>
                                        <option value="unconsumed"><?php echo e(trans('admin.unconsumed')); ?> </option>
                                    </select>
                                </div>
                                <div class="form-group col-lg-3" id="M1" style="display: none">
                                    <label class="form-label" for=""> <?php echo e(trans('admin.Depreciation_Method')); ?>  </label>
                                    <select class="select2 form-control w-100" name="Depreciation_Method" >
                                        <option value=""><?php echo e(trans('admin.Depreciation_Method')); ?> </option>
                                        <option value="Fixed"><?php echo e(trans('admin.Fixed')); ?> </option>
                                        <option value="decreasing"><?php echo e(trans('admin.decreasing')); ?> </option>
                                    </select>
                                </div>

                                <div class="form-group col-lg-3" style="display: none">
                                    <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Purchases_Date')); ?></label>
                                    <input type="date" name="Purchases_Date" value="<?php echo e(old('Purchases_Date')); ?>"  class="form-control" >
                                </div>
                                <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Operation_Date')); ?></label>
                                    <input type="date" name="Operation_Date" value="<?php echo e(old('Operation_Date')); ?>"  class="form-control" required>
                                </div>
                             <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Cost')); ?></label>
     <input type="number" step="any" name="Cost" value="<?php echo e(old('Cost')); ?>"  class="form-control" required onclick="Result()" onkeyup="Result()" id="Cost">
                                </div>
                                <div class="form-group col-lg-3" id="M2" style="display: none">
                                    <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Previous_Depreciation')); ?></label>
     <input type="number" step="any" name="Previous_Depreciation" value="<?php echo e(old('Previous_Depreciation')); ?>"  class="form-control"  onclick="Result()" onkeyup="Result()" id="Previous_Depreciation" >
                                </div>
                                <div class="form-group col-lg-3" id="M3" style="display: none">
                                    <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Asset_Net')); ?></label>
     <input type="number" step="any"  value="<?php echo e(old('Asset_Net')); ?>"  class="form-control"  id="Asset_Net" disabled>
     <input type="hidden"  name="Asset_Net"  id="Asset_NetHide">
                                </div>
                                <div class="form-group col-lg-3" id="M4" style="display: none">
                                    <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Annual_Depreciation_Ratio')); ?></label>
     <input type="number" step="any" name="Annual_Depreciation_Ratio" value="<?php echo e(old('Annual_Depreciation_Ratio')); ?>"  class="form-control"  onclick="Result()" onkeyup="Result()"  id="Annual_Depreciation_Ratio">
                                </div>
                               <div class="form-group col-lg-3" id="M5" style="display: none">
                                    <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Annual_Depreciation')); ?></label>
     <input type="number" step="any"  value="<?php echo e(old('Annual_Depreciation')); ?>"  class="form-control"  id="Annual_Depreciation" disabled>
     <input type="hidden"  name="Annual_Depreciation"  id="Annual_DepreciationHide">
                                </div>
                                <div class="form-group col-lg-3" id="M6" style="display: none">
                                    <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Life_Span')); ?></label>
     <input type="number" step="any"  value="<?php echo e(old('Life_Span')); ?>"  class="form-control"  id="Life_Span" disabled>
     <input type="hidden"  name="Life_Span"  id="Life_SpanHide">
                                </div>

                                <div class="form-group col-lg-3" style="display: none">
                                    <label class="form-label" for=""><?php echo e(trans('admin.Depreciation_Expenses')); ?>   </label>
          <select class="js-data-example-ajax form-control w-100 AccEx"  name="Depreciation_Expenses">
                                    </select>
                                </div>

                                <div class="form-group col-lg-3" id="M7" style="display: none">
                                    <label class="form-label" for=""><?php echo e(trans('admin.Depreciation_Complex')); ?>   </label>
                        <select class="js-data-example-ajax form-control w-100 AccCom"  name="Depreciation_Complex"  >
                                    </select>
                                </div>

                                     <div class="form-group col-lg-3" id="M8" style="display: none">
                                    <label class="form-label" for=""><?php echo e(trans('admin.Account_Ehlak')); ?>   </label>
                        <select class="select2 form-control w-100"  name="Ehlak"  >
                        <option value=""><?php echo e(trans('admin.Account_Ehlak')); ?></option>
                        <?php $__currentLoopData = $Ehlak; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ehl): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                          <option value="<?php echo e($ehl->id); ?>">

                      <?php echo e(app()->getLocale() == 'ar' ?$ehl->Name :$ehl->NameEn); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>

                                          <div class="form-group col-lg-3">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Coin')); ?></label>
                                            <select class="select2 form-control w-100" name="Coin" required>
                                                 <option value=""> <?php echo e(trans('admin.Coin')); ?></option>
                                            <?php $__currentLoopData = $Coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($coin->id); ?>" <?php if($coin->id == $Def->Coin): ?> selected <?php endif; ?>>

                                                         <?php echo e(app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                        <div class="form-group col-lg-3">
                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Draw')); ?></label>
                                            <input type="text" name="Draw" value="1" class="form-control" required>
                                        </div>
                            <div class="form-group col-lg-3">
                                            <label class="form-label" for=""> <?php echo e(trans('admin.Cost_Center')); ?> </label>
                                            <select class="select2 form-control w-100" name="Cost_Center">
                                            <option value=""> <?php echo e(trans('admin.Cost_Center')); ?></option>
                                            <?php $__currentLoopData = $CostCenters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cost): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($cost->id); ?>">

                                               <?php echo e(app()->getLocale() == 'ar' ?$cost->Arabic_Name :$cost->English_Name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>


                                      <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Image')); ?></label>
                                    <input type="file" name="Image">
                                </div>

                                                     <div class="form-group col-lg-3">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Branch')); ?></label>
                                            <select class="select2 form-control w-100" name="Branch">
                                                 <option value=""> <?php echo e(trans('admin.Branch')); ?></option>
                                            <?php $__currentLoopData = $Branchs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($branch->id); ?>">

                                       <?php echo e(app()->getLocale() == 'ar' ?$branch->Arabic_Name :$branch->English_Name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>

                                    <div class="form-group col-lg-3">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Sort_Asset')); ?></label>
                                <select class="select2 form-control w-100" onchange="sort()" id="Sort_Asset" name="Sort_Asset" required>
                                                 <option value=""> <?php echo e(trans('admin.Sort_Asset')); ?></option>
                                                 <option value="1" selected> <?php echo e(trans('admin.Start_Period')); ?></option>
                                                 <option value="2"> <?php echo e(trans('admin.Purchase')); ?></option>
                                            </select>
                                        </div>

                                               <div class="form-group col-lg-3" id="VEND" style="display: none">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Vendor')); ?></label>
                                            <select  class="select2 form-control w-100" id="vendor" name="Vendor" >

                                            </select>
                                        </div>

                                                 <div class="form-group col-lg-3" id="SAFE" style="display: none">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Safe')); ?></label>
                                            <select class="select2 form-control w-100" name="Safe" >
                                                 <option value=""> <?php echo e(trans('admin.Safe')); ?></option>
                                            <?php $__currentLoopData = $Safes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $safe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                             <option value="<?php echo e($safe->id); ?>">

                           <?php echo e(app()->getLocale() == 'ar' ?$safe->Name :$safe->NameEn); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>

                                              <div class="form-group col-lg-3" id="Payment">
                                            <label class="form-label" for=""> <?php echo e(trans('admin.Payment_Method')); ?></label>
         <select class="select2 form-control w-100" name="Payment_Method" id="Payment_Method" >
                                                <option value="Cash" ><?php echo e(trans('admin.Cash')); ?> </option>
                                                <option value="Later" ><?php echo e(trans('admin.Later')); ?></option>
                                                <option value="Installment"  ><?php echo e(trans('admin.Installment')); ?></option>
                                                <option value="Check" ><?php echo e(trans('admin.Check')); ?></option>
                                            </select>
                                        </div>
                                <div class="form-group col-lg-12">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Notes')); ?></label>
                                    <input type="text" name="Note" value="<?php echo e(old('Note')); ?>" class="form-control">
                                </div>

                               </div>
                            </div>
                            <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                                <button type="submit" class="btn btn-primary"><?php echo e(trans('admin.Save')); ?></button>
                            </div>
                        </form>
                        </div>
                    </div>
                </div>


<?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>


                 <!-- Modal Delete -->
                 <div class="modal fade" id="Delete<?php echo e($item->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered " role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h4 class="modal-title">
                                        <?php echo e(trans('admin.Delete')); ?>

                                </h4>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                   <?php echo e(trans('admin.RUSWDT')); ?> <strong><?php echo e($item->Code); ?></strong>
                            </div>
                            <div class="modal-footer">

                   <button type="button" class="btn btn-secondary" data-dismiss="modal"> <?php echo e(trans('admin.No')); ?></button>
                   <a href="<?php echo e(url('DeleteAssets/'.$item->id)); ?>"  class="btn btn-primary"> <?php echo e(trans('admin.Yes')); ?></a>
                            </div>
                        </div>
                    </div>
                </div>
      <!--modal show data-->
        <div class="modal fade" id="show-data<?php echo e($item->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h4 class="modal-title">
                                      <?php echo e(trans('admin.Data')); ?>

                                </h4>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                          <div style="overflow:auto">
                                        <table id=""
                                            class="table table-bordered table-hover table-striped" >
                                            <thead class="bg-highlight">
                                                <tr>

                                                    <th> <?php echo e(trans('admin.Asset_Type')); ?></th>
                                                    <th> <?php echo e(trans('admin.Depreciation_Method')); ?></th>

                                                    <th> <?php echo e(trans('admin.Operation_Date')); ?></th>
                                                    <th> <?php echo e(trans('admin.Cost')); ?></th>
                                                    <th> <?php echo e(trans('admin.Previous_Depreciation')); ?></th>
                                                    <th> <?php echo e(trans('admin.Asset_Net')); ?></th>
                                                    <th> <?php echo e(trans('admin.Annual_Depreciation_Ratio')); ?></th>
                                                    <th> <?php echo e(trans('admin.Annual_Depreciation')); ?></th>
                                                    <th> <?php echo e(trans('admin.Life_Span')); ?></th>
                                                    <th> <?php echo e(trans('admin.Draw')); ?></th>
                                                    <th> <?php echo e(trans('admin.Coin')); ?></th>
                                                    <th> <?php echo e(trans('admin.Cost_Center')); ?></th>

                                                    <th> <?php echo e(trans('admin.Note')); ?></th>
                                                    <th> <?php echo e(trans('admin.Branch')); ?></th>
                                                 <!--   <th> <?php echo e(trans('admin.Depreciation_Expenses')); ?></th> -->
                                                    <th> <?php echo e(trans('admin.Depreciation_Complex')); ?></th>
                                                    <th> <?php echo e(trans('admin.Sort_Asset')); ?></th>
                                                    <th> <?php echo e(trans('admin.Vendor')); ?></th>
                                                    <th> <?php echo e(trans('admin.Safe')); ?></th>
                                                    <th> <?php echo e(trans('admin.Main_Account')); ?></th>

                                                    <th> <?php echo e(trans('admin.Account_Ehlak')); ?></th>
                                                    <th> <?php echo e(trans('admin.User')); ?></th>


                                                </tr>
                                            </thead>
                                            <tbody>

                                                <tr>

                                                    <td>
                                     <?php echo e(app()->getLocale() == 'ar' ?$item->Asset_Type :$item->Asset_Type_En); ?>

                                                    </td>
                                                    <td>

                                         <?php echo e(app()->getLocale() == 'ar' ?$item->Depreciation_Method :$item->Depreciation_Method_En); ?>

                                                    </td>

                                                    <td><?php echo e($item->Operation_Date); ?></td>
                                                    <td><?php echo e($item->Cost); ?></td>
                                                    <td><?php echo e($item->Previous_Depreciation); ?></td>
                                                    <td><?php echo e($item->Asset_Net); ?></td>
                                                    <td><?php echo e($item->Annual_Depreciation_Ratio); ?></td>
                                                    <td><?php echo e($item->Annual_Depreciation); ?></td>
                                                    <td><?php echo e($item->Life_Span); ?></td>
                                                    <td><?php echo e($item->Draw); ?></td>
                                                    <td>
                                                        <?php if($item->Coin()->first()): ?>
                                                            <?php echo e($item->Coin()->first()->Arabic_Name); ?>

                                                        <?php else: ?>
                                                            <?php echo e(trans('admin.Not_Found')); ?>

                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                    <?php if(!empty($item->Cost_Center) && $item->Cost_Center()->first()): ?>
                                                        <?php echo e(app()->getLocale() == 'ar' ? $item->Cost_Center()->first()->Arabic_Name : $item->Cost_Center()->first()->English_Name); ?>

                                                    <?php elseif(!empty($item->Cost_Center)): ?>
                                                        <?php echo e(trans('admin.Not_Found')); ?>

                                                    <?php endif; ?>
                                                    </td>

                                                    <td><?php echo e($item->Note); ?></td>
                                                               <td>
                                                    <?php if(!empty($item->Branch) && $item->Branch()->first()): ?>
                                                        <?php echo e(app()->getLocale() == 'ar' ? $item->Branch()->first()->Arabic_Name : $item->Branch()->first()->English_Name); ?>

                                                    <?php elseif(!empty($item->Branch)): ?>
                                                        <?php echo e(trans('admin.Not_Found')); ?>

                                                    <?php endif; ?>
                                                    </td>
                                                <!--    <td>
                                                          <?php if(!empty($item->Depreciation_Expenses) && $item->Depreciation_Expenses()->first()): ?>
                                                            <?php echo e($item->Depreciation_Expenses()->first()->Name); ?>

                                                        <?php elseif(!empty($item->Depreciation_Expenses)): ?>
                                                            <?php echo e(trans('admin.Not_Found')); ?>

                                                        <?php endif; ?>
                                                       </td> -->

                                                                          <td>
                                                          <?php if(!empty($item->Depreciation_Complex) && $item->Depreciation_Complex()->first()): ?>
                                                            <?php echo e($item->Depreciation_Complex()->first()->Name); ?>

                                                        <?php elseif(!empty($item->Depreciation_Complex)): ?>
                                                            <?php echo e(trans('admin.Not_Found')); ?>

                                                        <?php endif; ?>
                                                       </td>

                                                                        <td>
                                                    <?php if($item->Sort_Asset == 1): ?>
                                                <?php echo e(trans('admin.Start_Period')); ?>

                                                    <?php elseif($item->Sort_Asset == 2): ?>
                                            <?php echo e(trans('admin.Purchase')); ?>

                                                    <?php endif; ?>
                                                    </td>

                                                                        <td>
                                                    <?php if(!empty($item->Vendor) && $item->Vendor()->first()): ?>
                                                        <?php echo e(app()->getLocale() == 'ar' ? $item->Vendor()->first()->Name : $item->Vendor()->first()->NameEn); ?>

                                                    <?php elseif(!empty($item->Vendor)): ?>
                                                        <?php echo e(trans('admin.Not_Found')); ?>

                                                    <?php endif; ?>
                                                    </td>

                                                                           <td>
                                                    <?php if(!empty($item->Safe) && $item->Safe()->first()): ?>
                                                        <?php echo e(app()->getLocale() == 'ar' ? $item->Safe()->first()->Name : $item->Safe()->first()->NameEn); ?>

                                                    <?php elseif(!empty($item->Safe)): ?>
                                                        <?php echo e(trans('admin.Not_Found')); ?>

                                                    <?php endif; ?>
                                                    </td>

                                                    <td>

                                        <?php if($item->Main_Account()->first()): ?>
                                            <?php echo e(app()->getLocale() == 'ar' ? $item->Main_Account()->first()->Name : $item->Main_Account()->first()->NameEn); ?>

                                        <?php else: ?>
                                            <?php echo e(trans('admin.Not_Found')); ?>

                                        <?php endif; ?>
                                                    </td>

                                                      <td>
                                                        <?php if(!empty($item->Ehlak) && $item->Ehlak()->first()): ?>
                                                            <?php echo e(app()->getLocale() == 'ar' ? $item->Ehlak()->first()->Name : $item->Ehlak()->first()->NameEn); ?>

                                                        <?php elseif(!empty($item->Ehlak)): ?>
                                                            <?php echo e(trans('admin.Not_Found')); ?>

                                                        <?php endif; ?>
                                                    </td>

                                                     <td>

                                        <?php if($item->User()->first()): ?>
                                            <?php echo e(app()->getLocale() == 'ar' ? $item->User()->first()->name : $item->User()->first()->nameEn); ?>

                                        <?php else: ?>
                                            <?php echo e(trans('admin.Not_Found')); ?>

                                        <?php endif; ?>
                                                    </td>


                                                </tr>

                                            </tbody>
                                            <tfoot>
                                                <tr>

                                                    <th> <?php echo e(trans('admin.Asset_Type')); ?></th>
                                                    <th> <?php echo e(trans('admin.Depreciation_Method')); ?></th>
                                                    <th> <?php echo e(trans('admin.Purchases_Date')); ?></th>
                                                    <th> <?php echo e(trans('admin.Operation_Date')); ?></th>
                                                    <th> <?php echo e(trans('admin.Cost')); ?></th>
                                                    <th> <?php echo e(trans('admin.Previous_Depreciation')); ?></th>
                                                    <th> <?php echo e(trans('admin.Asset_Net')); ?></th>
                                                    <th> <?php echo e(trans('admin.Annual_Depreciation_Ratio')); ?></th>
                                                    <th> <?php echo e(trans('admin.Annual_Depreciation')); ?></th>
                                                    <th> <?php echo e(trans('admin.Life_Span')); ?></th>
                                                    <th> <?php echo e(trans('admin.Draw')); ?></th>
                                                    <th> <?php echo e(trans('admin.Coin')); ?></th>
                                                    <th> <?php echo e(trans('admin.Cost_Center')); ?></th>

                                                    <th> <?php echo e(trans('admin.Note')); ?></th>
                                                    <th> <?php echo e(trans('admin.Branch')); ?></th>
                                                  <!--  <th> <?php echo e(trans('admin.Depreciation_Expenses')); ?></th> -->
                                                    <th> <?php echo e(trans('admin.Depreciation_Complex')); ?></th>
                                                    <th> <?php echo e(trans('admin.Sort_Asset')); ?></th>
                                                    <th> <?php echo e(trans('admin.Vendor')); ?></th>
                                                    <th> <?php echo e(trans('admin.Safe')); ?></th>
                                                    <th> <?php echo e(trans('admin.Main_Account')); ?></th>

                                                      <th> <?php echo e(trans('admin.Account_Ehlak')); ?></th>
                                                    <th> <?php echo e(trans('admin.User')); ?></th>

                                                </tr>
                                            </tfoot>
                                        </table>
                                       </div>
                            </div>
                            <div class="modal-footer">
                                  <button type="button" class="btn btn-secondary" data-dismiss="modal"> <?php echo e(trans('admin.Close')); ?></button>
                            </div>

                        </div>
                    </div>
                </div>

      <!-- Modal Asset Sale -->
                 <div class="modal fade" id="Sale<?php echo e($item->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h4 class="modal-title">
                                        <?php echo e(trans('admin.Asset_Sale')); ?>

                                </h4>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                </button>
                            </div>
                    <form action="<?php echo e(url('AssetSale')); ?>" method="post">
                        <?php echo csrf_field(); ?>

                            <div class="modal-body">
                           <div class="row">
                                             <div class="form-group col-lg-3">
                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.CurrentAssetPrice')); ?></label>
                                            <input type="number" step="any"   class="form-control" required>
                                        </div>
                                               <div class="form-group col-lg-3">
                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Amount')); ?></label>
                                            <input type="number" step="any" name="Amount"  class="form-control" required>
                                        </div>
                               <input type="hidden" name="ID" value="<?php echo e($item->id); ?>">
             <div class="form-group col-lg-3">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Client')); ?></label>
                      <select  class="select2 form-control w-100 client" name="Client" required>

                                            </select>
                                        </div>

                                                 <div class="form-group col-lg-3">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Safe')); ?></label>
                                            <select class="select2 form-control w-100" name="Safe" required>
                                                 <option value=""> <?php echo e(trans('admin.Safe')); ?></option>
                                            <?php $__currentLoopData = $Safes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $safe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                             <option value="<?php echo e($safe->id); ?>">
                             <?php echo e(app()->getLocale() == 'ar' ?$safe->Name :$safe->NameEn); ?>


                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>

                                </div>
                            </div>
                            <div class="modal-footer">

                   <button type="button" class="btn btn-secondary" data-dismiss="modal"> <?php echo e(trans('admin.Close')); ?></button>
                   <button type="submit" class="btn btn-primary"> <?php echo e(trans('admin.Save')); ?></button>

                            </div>
                    </form>
                        </div>
                    </div>
                </div>



<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
   <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">

 <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/summernote/summernote.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
    <script>
        //_fnFeatureHtmlLength();
        $(document).ready(function () {
            // Setup - add a text input to each footer cell
            $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
            $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
                var title = $(this).text();
                $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search ' + '" />');

                $('input', this).on('keyup change', function () {
                    if (table.column(i).search() !== this.value) {
                        table
                            .column(i)
                            .search(this.value)
                            .draw();
                    }
                });
            });
            var table = $('#dt-basic-example').DataTable(
                {
                    // responsive: true,
                    orderCellsTop: true,
                    fixedHeader: true,
                    lengthChange: true,

                    dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

                    buttons: [
                        {
                            extend: 'pageLength',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'colvis',
                            text: 'Column Visibility',
                            titleAttr: 'Col visibility',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            titleAttr: 'Generate PDF',
                            className: 'btn-outline-danger btn-sm mr-1'
                        },
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            titleAttr: 'Generate Excel',
                            className: 'btn-outline-success btn-sm mr-1'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            titleAttr: 'Generate CSV',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'copyHtml5',
                            text: 'Copy',
                            titleAttr: 'Copy to clipboard',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            titleAttr: 'Print Table',
                            className: 'btn-outline-primary btn-sm'
                        }
                    ],
                });
            $('.js-thead-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
            });

            $('.js-tbody-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
            });

        });

    </script>
     <script>
        var autoSave = $('#autoSave');
        var interval;
        var timer = function()
        {
            interval = setInterval(function()
            {
                //start slide...
                if (autoSave.prop('checked'))
                    saveToLocal();

                clearInterval(interval);
            }, 3000);
        };

        //save
        var saveToLocal = function()
        {
            localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
            console.log("saved");
        }

        //delete
        var removeFromLocal = function()
        {
            localStorage.removeItem("summernoteData");
            $('#saveToLocal').summernote('reset');
        }

        $(document).ready(function()
        {
            //init default
            $('.js-summernote').summernote(
            {
                height: 200,
                tabsize: 2,
                placeholder: "Type here...",
                dialogsFade: true,
                toolbar: [
                    ['style', ['style']],
                    ['font', ['strikethrough', 'superscript', 'subscript']],
                    ['font', ['bold', 'italic', 'underline', 'clear']],
                    ['fontsize', ['fontsize']],
                    ['fontname', ['fontname']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['height', ['height']]
                    ['table', ['table']],
                    ['insert', ['link', 'picture', 'video']],
                    ['view', ['fullscreen', 'codeview', 'help']]
                ],
                callbacks:
                {
                    //restore from localStorage
                    onInit: function(e)
                    {
                        $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
                    },
                    onChange: function(contents, $editable)
                    {
                        clearInterval(interval);
                        timer();
                    }
                }
            });

            //load emojis
            $.ajax(
            {
                url: 'https://api.github.com/emojis',
                async: false
            }).then(function(data)
            {
                window.emojis = Object.keys(data);
                window.emojiUrls = data;
            });

            //init emoji example
            $(".js-hint2emoji").summernote(
            {
                height: 100,
                toolbar: false,
                placeholder: 'type starting with : and any alphabet',
                hint:
                {
                    match: /:([\-+\w]+)$/,
                    search: function(keyword, callback)
                    {
                        callback($.grep(emojis, function(item)
                        {
                            return item.indexOf(keyword) === 0;
                        }));
                    },
                    template: function(item)
                    {
                        var content = emojiUrls[item];
                        return '<img src="' + content + '" width="20" /> :' + item + ':';
                    },
                    content: function(item)
                    {
                        var url = emojiUrls[item];
                        if (url)
                        {
                            return $('<img />').attr('src', url).css('width', 20)[0];
                        }
                        return '';
                    }
                }
            });

            //init mentions example
            $(".js-hint2mention").summernote(
            {
                height: 100,
                toolbar: false,
                placeholder: "type starting with @",
                hint:
                {
                    mentions: ['jayden', 'sam', 'alvin', 'david'],
                    match: /\B@(\w*)$/,
                    search: function(keyword, callback)
                    {
                        callback($.grep(this.mentions, function(item)
                        {
                            return item.indexOf(keyword) == 0;
                        }));
                    },
                    content: function(item)
                    {
                        return '@' + item;
                    }
                }
            });

        });

    </script>


   <!-- Search Selecet -->
 <script>
        $(document).ready(function()
        {
            $(function()
            {
                $('.select2').select2();

                $(".select2-placeholder-multiple").select2(
                {
                    placeholder: "Select State"
                });
                $(".js-hide-search").select2(
                {
                    minimumResultsForSearch: 1 / 0
                });
                $(".js-max-length").select2(
                {
                    maximumSelectionLength: 2,
                    placeholder: "Select maximum 2 items"
                });
                $(".select2-placeholder").select2(
                {
                    placeholder: "Select a state",
                    allowClear: true
                });

                $(".js-select2-icons").select2(
                {
                    minimumResultsForSearch: 1 / 0,
                    templateResult: icon,
                    templateSelection: icon,
                    escapeMarkup: function(elm)
                    {
                        return elm
                    }
                });

                function icon(elm)
                {
                    elm.element;
                    return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
                }


  $('#Main_Account').select2({
    placeholder: "select...",
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllMainAssetsAccounts',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };

            	console.log(data);

        },
     data: function (params) {


                   var query = {
                            search: params.term,
                        };


              $.ajax({
                              url: 'AllMainAssetsAccountsJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('#Main_Account').empty();
                                  $.each(data, function(key, value){

                         $('#Main_Account').append('<option value="'+ key +'">' + value + '</option>');

                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });



        }
    }
  });


$('#Main_Account').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});


          $('#Main_New_Account').select2({
    placeholder: "select...",
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllMainAssetsAccounts',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };

            	console.log(data);

        },
     data: function (params) {


                   var query = {
                            search: params.term,
                        };


              $.ajax({
                              url: 'AllMainAssetsAccountsJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('#Main_Account').empty();
                                  $.each(data, function(key, value){

                         $('#Main_Account').append('<option value="'+ key +'">' + value + '</option>');

                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });



        }
    }
  });


$('#Main_New_Account').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});



  $('.AccEx').select2({
    placeholder: "select...",
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllAccountsExpenses',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };

            	console.log(data);

        },
         data: function (params) {


                   var query = {
                            search: params.term,
                        };


              $.ajax({
                              url: 'AllAccountsExpensesJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('.AccEx').empty();
                                  $.each(data, function(key, value){

                         $('.AccEx').append('<option value="'+ key +'">' + value + '</option>');

                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });



        }
    }
  });


$('.AccEx').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});


 $('.AccCom').select2({
    placeholder: "select...",
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllAccountsComplex',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };

            	console.log(data);

        },
           data: function (params) {


                   var query = {
                            search: params.term,
                        };


              $.ajax({
                              url: 'AllAccountsComplexJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('.AccCom').empty();
                                  $.each(data, function(key, value){

                         $('.AccCom').append('<option value="'+ key +'">' + value + '</option>');

                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });



        }
    }
  });


$('.AccCom').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});



                  $("#vendor").select2({
                placeholder: "select...",
                ajax: {
                    type: "GET",
                    dataType: "json",
                    url: "AllVendors",
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (obj, index) {

                                return { id: index, text: obj };
                            }),
                        };

                        console.log(data);
                    },
                     data: function (params) {


                   var query = {
                            search: params.term,
                        };


              $.ajax({
                              url: 'AllVendorsJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('#vendor').empty();
                                  $.each(data, function(key, value){

                         $('#vendor').append('<option value="'+ key +'">' + value + '</option>');

                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });



        }
                },
            });

            $("#vendor").on("select2:select", function (e) {
                console.log("select done", e.params.data);
            });

               $(".client").select2({
                placeholder: "select...",
                ajax: {
                    type: "GET",
                    dataType: "json",
                    url: "AllClientsFilter",
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (obj, index) {

                                return { id: index, text: obj };
                            }),
                        };

                        console.log(data);
                    },
                     data: function (params) {


                   var query = {
                            search: params.term,
                        };


              $.ajax({
                              url: 'AllClientsFilterJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('.client').empty();
                                  $.each(data, function(key, value){

                         $('.client').append('<option value="'+ key +'">' + value + '</option>');

                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });



        }
                },
            });

            $(".client").on("select2:select", function (e) {
                console.log("select done", e.params.data);
            });



            });
        });



    </script>


<script>
    function Result(){

       var  Cost =$('#Cost').val();
       var  Previous_Depreciation =$('#Previous_Depreciation').val();
       var  Annual_Depreciation_Ratio =$('#Annual_Depreciation_Ratio').val();


        var Net= parseFloat(Cost) -  parseFloat(Previous_Depreciation) ;

        $('#Asset_Net').val(parseFloat(Net));
        $('#Asset_NetHide').val(parseFloat(Net));

        var  Asset_Net =$('#Asset_Net').val();

        var i = parseFloat(Annual_Depreciation_Ratio) / 100 ;
        var ii = parseFloat(Cost) *  parseFloat(i) ;

        $('#Annual_Depreciation').val(parseFloat(ii));
        $('#Annual_DepreciationHide').val(parseFloat(ii));

         var  Annual_Depreciation =$('#Annual_Depreciation').val();

        var x = parseFloat(Cost) / parseFloat(Annual_Depreciation) ;

          $('#Life_Span').val(parseFloat(x));
        $('#Life_SpanHide').val(parseFloat(x));


    }

</script>

<script>
function Appear(){

var type= $('#type').val();
    if(type == "consumer"){

        document.getElementById("M1").style.display = "block";
        document.getElementById("M2").style.display = "block";
        document.getElementById("M3").style.display = "block";
        document.getElementById("M4").style.display = "block";
        document.getElementById("M5").style.display = "block";
        document.getElementById("M6").style.display = "block";
        document.getElementById("M7").style.display = "block";
        document.getElementById("M8").style.display = "block";

    }else{

        document.getElementById("M1").style.display = "none";
        document.getElementById("M2").style.display = "none";
        document.getElementById("M3").style.display = "none";
        document.getElementById("M4").style.display = "none";
        document.getElementById("M5").style.display = "none";
        document.getElementById("M6").style.display = "none";
        document.getElementById("M7").style.display = "none";
        document.getElementById("M8").style.display = "none";



    }

}
</script>

<script>
function sort(){
    var Sort_Asset =$('#Sort_Asset').val();

    if(Sort_Asset == 2){

 document.getElementById("VEND").style.display = "block";
 document.getElementById("SAFE").style.display = "block";
 document.getElementById("Payment").style.display = "block";

    }else{


 document.getElementById("VEND").style.display = "none";
 document.getElementById("SAFE").style.display = "none";
 document.getElementById("Payment").style.display = "none";
    }


}
</script>

<?php $__env->stopPush(); ?>


<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\erp\resources\views/admin/Accounts/Assets.blade.php ENDPATH**/ ?>
<?php $__env->startSection('content'); ?>
<?php
use App\Models\DefaultDataShowHide;
$show=DefaultDataShowHide::orderBy('id','desc')->first();
?>
<title><?php echo e(trans('admin.Insurance_Paper')); ?></title>
     <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Accounts')); ?></a></li>
                        <li class="breadcrumb-item"><?php echo e(trans('admin.Insurance_Paper')); ?></li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                class="js-get-date"></span></li>
                    </ol>


                    <!-- data entry -->
            <div class="panel-container show">
                                    <div class="panel-content">


     <form action="<?php echo e(url('InsurancePaperSechduleFilter')); ?>" method="get">

                                        <div class="form-row">
                                            <div class="form-group col-md-3 col-4-print">
                                                <label class="form-label" for="simpleinput"><?php echo e(trans('admin.From')); ?></label>
                                                <input type="date" id="from" value="<?php echo e(date('Y-m-d')); ?>" name="From" class="form-control">
                                            </div>
                                            <div class="form-group col-md-3 col-4-print">
                                                <label class="form-label" for="simpleinput"><?php echo e(trans('admin.To')); ?></label>
                                                <input type="date" id="to" value="<?php echo e(date('Y-m-d')); ?>" name="To" class="form-control">
                                            </div>

                                                 <div class="form-group col-lg-3">
                                    <label class="form-label" for=""> <?php echo e(trans('admin.Account_Name')); ?> </label>
                    <select class="js-data-example-ajax form-control w-100 Acc" name="Account"  >
                                    </select>
                                </div>


                                                             <div class="form-group col-md-3 col-4-print">
                                                <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Due_Date')); ?></label>
                                                <input type="date" id="Due_Date"  name="Due_Date" class="form-control">
                                            </div>


                                            <div class="form-group col-md-3 col-4-print">
                                                <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Code')); ?></label>
                                                <input type="text" id="Code"  name="Code" class="form-control">
                                            </div>
                                            <div class="buttons m-2">
                                            <button type="submit" class="btn btn-primary"><i class="fal fa-search"></i></button>
                                                </div>
          </div>
                                        </form>
                                    </div>
                                </div>

                    <div class="row">
                        <div class="col-xl-12">
                            <div id="panel-1" class="panel">
                                <div class="panel-hdr">
                                   <h2>

                                      <?php echo e(trans('admin.Insurance_Paper')); ?>

                                   </h2>

                                    <div class="panel-toolbar">
                                          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه وصل امانه')): ?>
                                    <button type="button" class="btn btn-default btn-sm margin-btn" data-toggle="modal" data-target="#default-example-modal-center-add">
                                        <?php echo e(trans('admin.AddNew')); ?></button>
                                       <?php endif; ?>
                                        <button class="btn btn-primary btn-sm" data-toggle="dropdown">Table
                                            Style</button>
                                        <div
                                            class="dropdown-menu dropdown-menu-animated dropdown-menu-right position-absolute pos-top">
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-bordered" data-target="#dt-basic-example"> Bordered
                                                Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-sm"
                                                data-target="#dt-basic-example"> Smaller Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-dark"
                                                data-target="#dt-basic-example"> Table Dark </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-hover" data-target="#dt-basic-example"> Table Hover
                                            </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-stripe" data-target="#dt-basic-example"> Table
                                                Stripped </button>
                                            <div class="dropdown-divider m-0"></div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    tbody color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-tbody-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    thead color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-thead-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="panel-container show">
                                            <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                    <div class="panel-content">

                                        <!-- datatable start -->
                                        <div style="overflow:auto;">
                                        <table id="dt-basic-example"
                                            class="table table-bordered table-hover table-striped" style="width:100%;">
                                            <thead class="bg-highlight">
                                                <tr>
                                                    <th><?php echo e(trans('admin.Code')); ?></th>
                                                    <th><?php echo e(trans('admin.Date')); ?></th>
                                                    <th> <?php echo e(trans('admin.Account_Name')); ?></th>
                                                    <th> <?php echo e(trans('admin.From_Ar_Name')); ?></th>
                                                    <th> <?php echo e(trans('admin.To_Ar_Name')); ?></th>
                                                    <th> <?php echo e(trans('admin.From_En_Name')); ?></th>
                                                    <th> <?php echo e(trans('admin.To_En_Name')); ?></th>
                                                    <th> <?php echo e(trans('admin.Due_Date')); ?></th>
                                                    <th> <?php echo e(trans('admin.Amount')); ?></th>

                                                     <th>  <?php echo e(trans('admin.Data')); ?></th>
                                                     <th>  <?php echo e(trans('admin.File')); ?></th>
                                                    <th style="padding: 13px 36px;"> <?php echo e(trans('admin.Actions')); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php echo e($item->Code); ?></td>
                                                    <td><?php echo e($item->Date); ?></td>
                                              <td>
                                                         <?php if($item->Account()->first()): ?>
                                                             <?php echo e(app()->getLocale() == 'ar' ? $item->Account()->first()->Name : $item->Account()->first()->NameEn); ?>

                                                         <?php else: ?>
                                                             <?php echo e(trans('admin.Not_Found')); ?>

                                                         <?php endif; ?>
                                                    </td>
                                                    <td><?php echo e($item->From); ?></td>
                                                    <td><?php echo e($item->To); ?></td>
                                                    <td><?php echo e($item->FromEn); ?></td>
                                                    <td><?php echo e($item->ToEn); ?></td>

                                                      <td><?php echo e($item->Due_Date); ?></td>

                                                    <td><?php echo e($item->Amount); ?></td>



                                                       <td>
                                                          <button type="button" class="btn btn-default" data-toggle="modal" data-target="#show-data<?php echo e($item->id); ?>">
                                                                        <?php echo e(trans('admin.Data')); ?>

                                                        </button>
                                                     </td>
                                                                  <td>
                                <?php if(!empty($item->File)): ?>
                            <a href="<?php echo e(URL::to($item->File)); ?>" class="btn btn-primary" dowmload><i class="fal fa-download"></i></a>
                                        <?php endif; ?>
                                    </td>
                                                    <td>

                    <?php if($item->Status == 0): ?>
       <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('حذف وصل امانه')): ?>
        <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Delete<?php echo e($item->id); ?>">
                                                                        <i class="fal fa-trash"></i>
                                                        </button>
                                                        <?php endif; ?>

                   <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('استلام وصل امانه')): ?>
        <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Recived<?php echo e($item->id); ?>">
                                                                       <i class="fal fa-check"></i>
                                                        </button>
                                            <?php endif; ?>
                                    <?php endif; ?>
                                                 <button type="button" class="btn btn-default" data-toggle="modal" data-target="#print<?php echo e($item->id); ?>">
                                                                       <i class="fal fa-print"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>



                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                  <th><?php echo e(trans('admin.Code')); ?></th>
                                                    <th><?php echo e(trans('admin.Date')); ?></th>
                                                    <th> <?php echo e(trans('admin.Account_Name')); ?></th>
                                            <th> <?php echo e(trans('admin.From_Ar_Name')); ?></th>
                                                    <th> <?php echo e(trans('admin.To_Ar_Name')); ?></th>
                                                    <th> <?php echo e(trans('admin.From_En_Name')); ?></th>
                                                    <th> <?php echo e(trans('admin.To_En_Name')); ?></th>
                                                    <th> <?php echo e(trans('admin.Due_Date')); ?></th>
                                                    <th> <?php echo e(trans('admin.Amount')); ?></th>

                                                     <th> <?php echo e(trans('admin.Data')); ?></th>
                                                     <th> <?php echo e(trans('admin.File')); ?></th>
                                                    <th> <?php echo e(trans('admin.Actions')); ?></th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                        </div>
                                       <?php echo e($items->Links()); ?>

                                    </div>
                                </div>
                            </div>
                        </div>
         </div>
</main>


                 <!-- Modal Add -->
     <div class="modal fade" id="default-example-modal-center-add" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h4 class="modal-title">
                                   <?php echo e(trans('admin.AddNew')); ?>

                                </h4>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                </button>
                            </div>
                    <form action="<?php echo e(url('AddInsurancePaper')); ?>" method="post" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>

                              <?php echo view('honeypot::honeypotFormFields'); ?>
                            <div class="modal-body">

                               <div class="row">
                                <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Code')); ?></label>
                                    <input type="text"  value="<?php echo e($Code); ?>" disabled class="form-control">
                                    <input type="hidden" name="Code"  value="<?php echo e($Code); ?>">
                                </div>
                                 <?php if(auth()->guard('admin')->user()->emp == 0): ?>
                      <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Date')); ?></label>
                                    <input type="date" name="Date" value="<?php echo e(date('Y-m-d')); ?>" class="form-control" required>
                                </div>
                                        <?php else: ?>

                                         <?php if(auth()->guard('admin')->user()->Date == 1): ?>
                                   <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Date')); ?></label>
                                    <input type="date" name="Date" value="<?php echo e(date('Y-m-d')); ?>" class="form-control" required>
                                </div>
                                        <?php else: ?>
                                 <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Date')); ?></label>
                                    <input type="date" name="Date" value="<?php echo e(date('Y-m-d')); ?>" class="form-control" required readonly>
                                </div>
                                        <?php endif; ?>

                                        <?php endif; ?>

                                    <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.From_Ar_Name')); ?></label>
                                    <input type="text" name="From" value="<?php echo e(old('From')); ?>" class="form-control" required>
                                </div>

                                            <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.To_Ar_Name')); ?></label>
                                    <input type="text" name="To" value="<?php echo e(old('To')); ?>" class="form-control" required>
                                </div>


                                              <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.From_En_Name')); ?></label>
                                    <input type="text" name="FromEn" value="<?php echo e(old('FromEn')); ?>" class="form-control" >
                                </div>

                                            <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.To_En_Name')); ?></label>
                                    <input type="text" name="ToEn" value="<?php echo e(old('ToEn')); ?>" class="form-control" >
                                </div>

                                <div class="form-group col-lg-3">
                                    <label class="form-label" for=""> <?php echo e(trans('admin.Account_Name')); ?> </label>
                    <select class="js-data-example-ajax form-control w-100 Acc" name="Account"  required>
                                    </select>
                                </div>



                                <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Due_Date')); ?></label>
                                    <input type="date" name="Due_Date" value="<?php echo e(old('Due_Date')); ?>" class="form-control" required>
                                </div>

                                <div class="form-group col-lg-3">
                                    <label class="form-label" for=""><?php echo e(trans('admin.Coin')); ?>   </label>
                                    <select class="select2 form-control w-100" name="Coin"  required>
                                          <option value=""><?php echo e(trans('admin.Coin')); ?></option>
                                     <?php $__currentLoopData = $Coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                           <option value="<?php echo e($coin->id); ?>" >
        <?php echo e(app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name); ?>

                                        </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Draw')); ?></label>
                      <input type="number" step="any" name="Draw" value="1" class="form-control" required />
                                </div>

                                <div class="form-group col-lg-3">
                                    <label class="form-label" for=""><?php echo e(trans('admin.Cost_Center')); ?>   </label>
                                    <select class="select2 form-control w-100" name="Cost_Center">
                                        <option value=""><?php echo e(trans('admin.Cost_Center')); ?> </option>
                                     <?php $__currentLoopData = $CostCenters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cost): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($cost->id); ?>">
        <?php echo e(app()->getLocale() == 'ar' ?$cost->Arabic_Name :$cost->English_Name); ?>

                                        </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Amount')); ?></label>
                                    <input type="number" step="any" value="<?php echo e(old('Amount')); ?>" name="Amount" class="form-control" required>
                                </div>
                                <div class="form-group col-lg-12">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Notes')); ?></label>
                                    <input type="text" name="Note" value="<?php echo e(old('Note')); ?>" class="form-control">
                                </div>


                                         <?php if($show && $show->Show_File_InsurancePaper == 1): ?>
                                     <div class="form-group col-lg-12">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.File')); ?></label>
                                    <input type="file" name="File"  class="form-control">
                                </div>
                                <?php endif; ?>


                               </div>
                            </div>
                            <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                                <button type="submit" class="btn btn-primary"><?php echo e(trans('admin.Save')); ?></button>
                            </div>
                        </form>
                        </div>
                    </div>
                </div>


<?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                 <!-- Modal Delete -->
                 <div class="modal fade" id="Delete<?php echo e($item->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered " role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h4 class="modal-title">
                                        <?php echo e(trans('admin.Delete')); ?>

                                </h4>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                   <?php echo e(trans('admin.RUSWDT')); ?> <strong><?php echo e($item->Code); ?></strong>
                            </div>
                            <div class="modal-footer">
                   <button type="button" class="btn btn-secondary" data-dismiss="modal"> <?php echo e(trans('admin.No')); ?></button>
                   <a href="<?php echo e(url('DeleteInsurancePaper/'.$item->id)); ?>"  class="btn btn-primary"> <?php echo e(trans('admin.Yes')); ?></a>
                            </div>
                        </div>
                    </div>
                </div>
                 <!--Modal show data-->
                 <div class="modal fade" id="show-data<?php echo e($item->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h4 class="modal-title">
                                       <?php echo e(trans('admin.Data')); ?>

                                </h4>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div style="overflow:auto">
                                                                        <table
                                            class="table table-bordered table-hover table-striped" >
                                            <thead class="bg-highlight">
                                                <tr>

                                                    <th> <?php echo e(trans('admin.Coin')); ?></th>
                                                    <th> <?php echo e(trans('admin.Draw')); ?></th>
                                                    <th> <?php echo e(trans('admin.Coin_Amount')); ?></th>
                                                    <th> <?php echo e(trans('admin.Cost_Center')); ?></th>
                                                    <th> <?php echo e(trans('admin.Notes')); ?></th>
                                                    <th> <?php echo e(trans('admin.Status')); ?></th>
                                                    <th> <?php echo e(trans('admin.Bank')); ?></th>
                                                    <th> <?php echo e(trans('admin.User')); ?></th>

                                                </tr>
                                            </thead>
                                            <tbody>

                                                <tr>


                                                                   <td>
                                                         <?php if($item->Coin()->first()): ?>
                                                             <?php echo e(app()->getLocale() == 'ar' ? $item->Coin()->first()->Arabic_Name : $item->Coin()->first()->English_Name); ?>

                                                         <?php else: ?>
                                                             <?php echo e(trans('admin.Not_Found')); ?>

                                                         <?php endif; ?>
                                                    </td>
                                                     <td><?php echo e($item->Draw); ?></td>
                                                     <td><?php echo e($item->Draw * $item->Amount); ?></td>
                                                     <td>

                                         <?php if(!empty($item->Cost_Center) && $item->Cost_Center()->first()): ?>
                                             <?php echo e(app()->getLocale() == 'ar' ? $item->Cost_Center()->first()->Arabic_Name : $item->Cost_Center()->first()->English_Name); ?>

                                         <?php elseif(!empty($item->Cost_Center)): ?>
                                             <?php echo e(trans('admin.Not_Found')); ?>

                                         <?php endif; ?>
                                                    </td>

                                                     <td><?php echo e($item->Note); ?></td>
                                                     <td>
                                                    <?php if($item->Status == 0): ?>
                                                         <?php echo e(trans('admin.Waiting')); ?>


                                                    <?php elseif($item->Status == 1): ?>

                                                       <?php echo e(trans('admin.Recived')); ?>  <br>

                                                         <?php endif; ?>
                                                    </td>
                                                         <td>
                                                   <?php if(!empty($item->Bank) && $item->Bank()->first()): ?>
                                                       <?php echo e(app()->getLocale() == 'ar' ? $item->Bank()->first()->Name : $item->Bank()->first()->NameEn); ?>

                                                   <?php elseif(!empty($item->Bank)): ?>
                                                       <?php echo e(trans('admin.Not_Found')); ?>

                                                   <?php endif; ?>
                                                    </td>
                                                     <td>
                                          <?php if($item->User()->first()): ?>
                                              <?php echo e(app()->getLocale() == 'ar' ? $item->User()->first()->name : $item->User()->first()->nameEn); ?>

                                          <?php else: ?>
                                              <?php echo e(trans('admin.Not_Found')); ?>

                                          <?php endif; ?>
                                                    </td>

                                                </tr>




                                            </tbody>
                                            <tfoot>
                                                <tr>

                                                    <th> <?php echo e(trans('admin.Coin')); ?></th>
                                                    <th> <?php echo e(trans('admin.Draw')); ?></th>
                                                    <th> <?php echo e(trans('admin.Coin_Amount')); ?></th>
                                                    <th> <?php echo e(trans('admin.Cost_Center')); ?></th>
                                                    <th> <?php echo e(trans('admin.Notes')); ?></th>
                                                    <th> <?php echo e(trans('admin.Status')); ?></th>
                                                    <th> <?php echo e(trans('admin.Bank')); ?></th>
                                                    <th> <?php echo e(trans('admin.User')); ?></th>

                                                </tr>
                                            </tfoot>
                                        </table>
                            </div>
                            </div>
                            <div class="modal-footer">

                   <button type="button" class="btn btn-secondary" data-dismiss="modal"> <?php echo e(trans('admin.Close')); ?></button>

                            </div>
                        </div>
                    </div>
                </div>

      <!-- Modal Recived -->
                 <div class="modal fade" id="Recived<?php echo e($item->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered " role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h4 class="modal-title">
                                        <?php echo e(trans('admin.Recived')); ?>

                                </h4>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                </button>
                            </div>
                    <form action="<?php echo e(url('RecivedInurance/'.$item->id)); ?>" method="post">
                        <?php echo csrf_field(); ?>

                            <div class="modal-body">

                                           <div class="form-group col-lg-12">
                                    <label class="form-label" for=""><?php echo e(trans('admin.Bank')); ?>   </label>
                        <select class="js-data-example-ajax form-control w-100"  name="Bank" id="Bank" required>
                                    </select>
                                </div>

                                         <div class="modal-footer">
                   <button type="button" class="btn btn-secondary" data-dismiss="modal"> <?php echo e(trans('admin.Close')); ?></button>
                   <button type="submit" class="btn btn-secondary"> <?php echo e(trans('admin.Recived')); ?></button>
                            </div>
                            </div>
                   </form>
                        </div>
                    </div>
                </div>
      <!-- Modal Print -->
                 <div class="modal fade" id="print<?php echo e($item->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog modal-xl modal-dialog-centered " role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h4 class="modal-title">
<?php echo e(trans('admin.Insurance_Paper')); ?>

                                </h4>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                </button>
                            </div>

                            <div class="modal-body">
                               <div class="row">
                                   <div class="col-lg-12">
                                       <div><span>
                                           <?php echo e(trans('admin.I_received')); ?>

                                             :</span>
                                       <?php if($item->Account()->first()): ?>
                                           <?php echo e(app()->getLocale() == 'ar' ? $item->Account()->first()->Name : $item->Account()->first()->NameEn); ?>

                                       <?php else: ?>
                                           <?php echo e(trans('admin.Not_Found')); ?>

                                       <?php endif; ?>
                                       </div>
                                   </div>
                                   <div class="col-lg-6 col-6">
                                       <div><span>     <?php echo e(trans('admin.Resident')); ?> :</span> ..............</div>
                                   </div>
                                   <div class="col-lg-3 col-3">
                                       <div><span> <?php echo e(trans('admin.Center')); ?> :</span>............</div>
                                   </div>
                                   <div class="col-lg-3 col-3">
                                       <div><span> <?php echo e(trans('admin.Governrate')); ?> :</span>.............</div>
                                   </div>
                                   <div class="col-lg-4 col-4">
                                       <div><span>  <?php echo e(trans('admin.ID_Number')); ?> :</span>........</div>
                                   </div>
                                   <div class="col-lg-4 col-4">
                                       <div><span><?php echo e(trans('admin.Personal_family')); ?>  <?php echo e(trans('admin.Coming_From')); ?> :</span>.........</div>
                                   </div>
                                   <div class="col-lg-4 col-4">
                                       <div><span><?php echo e(trans('admin.Dately')); ?> :</span>.........</div>
                                   </div>
                                   <div class="col-lg-6 col-6">
                                       <div><span> <?php echo e(trans('admin.From_Mr')); ?> :</span>
                                           <?php echo e(app()->getLocale() == 'ar' ?$item->From :$item->FromEn); ?>   </div>
                                   </div>
                                   <div class="col-lg-2 col-2">
                                       <div><span> <?php echo e(trans('admin.Resident')); ?> :</span>........</div>
                                   </div>
                                   <div class="col-lg-2 col-2">
                                       <div><span> <?php echo e(trans('admin.Center')); ?> :</span>..........</div>
                                   </div>
                                   <div class="col-lg-2 col-2">
                                       <div><span><?php echo e(trans('admin.Governrate')); ?> :</span>...........</div>
                                   </div>
                                         <div class="col-lg-4 col-4">
                                       <div><span>  <?php echo e(trans('admin.ID_Number')); ?> :</span>........</div>
                                   </div>
                                   <div class="col-lg-4 col-4">
                                       <div><span> <?php echo e(trans('admin.Amount_Of')); ?> :</span><?php echo e($item->Amount); ?>  <?php echo e(trans('admin.Just')); ?></div>
                                   </div>
                                   <div class="col-lg-4 col-4">
                                       <div><span class="bg-color">
                                           <?php
                                               try {
                                                   if(class_exists('NumberFormatter')) {
                                                       if(app()->getLocale() == 'ar' ){
                                                           $f = new NumberFormatter("ar", NumberFormatter::SPELLOUT);
                                                           echo $f->format(round($item->Amount));
                                                       } else {
                                                           $f = new NumberFormatter("en", NumberFormatter::SPELLOUT);
                                                           echo $f->format(round($item->Amount));
                                                       }
                                                   } else {
                                                       // Fallback when NumberFormatter is not available
                                                       echo round($item->Amount) . ' ' . (app()->getLocale() == 'ar' ? 'فقط' : 'only');
                                                   }
                                               } catch (Exception $e) {
                                                   // Fallback in case of any error
                                                   echo round($item->Amount) . ' ' . (app()->getLocale() == 'ar' ? 'فقط' : 'only');
                                               }
                                           ?>
                                           </span>

                                    </div>
                                   </div>
                                   <div class="col-lg-4 col-4">
                                       <div>   ...................... </div>
                                   </div>
                                   <div class="col-lg-12">
                                       <div><span> <?php echo e(trans('admin.Insurance_Letter')); ?> :</span> <?php echo e(app()->getLocale() == 'ar' ?$item->To :$item->En); ?> </div>
                                   </div>
                                    <div class="col-lg-4 col-4">
                                       <div><span> <?php echo e(trans('admin.Resident')); ?> :</span>........</div>
                                   </div>
                                   <div class="col-lg-4 col-4">
                                       <div><span><?php echo e(trans('admin.Center')); ?> :</span>..........</div>
                                   </div>
                                   <div class="col-lg-4 col-4">
                                       <div><span><?php echo e(trans('admin.Governrate')); ?> :</span>...........</div>
                                   </div>
                                              <div class="col-lg-4 col-4">
                                       <div><span>  <?php echo e(trans('admin.ID_Number')); ?> :</span>........</div>
                                   </div>

                                   <div class="col-lg-12 ">
                              <?php echo e(trans('admin.Insurance_Letter2')); ?>

                                   </div>
                                   <div class="col-lg-12 text-center">

                                   <?php echo e(trans('admin.Insurance_Letter3')); ?>       ...
                                   </div>
                                   <div class="col-lg-8 col-8">

                                   </div>
                                   <div class="col-lg-4 col-4 text-center">
                                       <p> <?php echo e(trans('admin.Recipients')); ?> </p>
                                       <div><span class="bg-color">..........</span></div>
                                   </div>
                               </div>

                                         <div class="modal-footer">
                   <button type="button" class="btn btn-secondary" data-dismiss="modal"> <?php echo e(trans('admin.Close')); ?></button>
                   <button type="submit" class="btn btn-secondary"  onclick="window.print()"><?php echo e(trans('admin.Print')); ?></button>
                            </div>
                            </div>

                        </div>
                    </div>
                </div>



<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>



<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
   <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">

 <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/summernote/summernote.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
    <script>
        //_fnFeatureHtmlLength();
        $(document).ready(function () {
            // Setup - add a text input to each footer cell
            $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
            $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
                var title = $(this).text();
                $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search ' + '" />');

                $('input', this).on('keyup change', function () {
                    if (table.column(i).search() !== this.value) {
                        table
                            .column(i)
                            .search(this.value)
                            .draw();
                    }
                });
            });
            var table = $('#dt-basic-example').DataTable(
                {
                    // responsive: true,
                    orderCellsTop: true,
                    fixedHeader: true,
                    lengthChange: true,

                    dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

                    buttons: [
                        {
                            extend: 'pageLength',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'colvis',
                            text: 'Column Visibility',
                            titleAttr: 'Col visibility',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            titleAttr: 'Generate PDF',
                            className: 'btn-outline-danger btn-sm mr-1'
                        },
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            titleAttr: 'Generate Excel',
                            className: 'btn-outline-success btn-sm mr-1'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            titleAttr: 'Generate CSV',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'copyHtml5',
                            text: 'Copy',
                            titleAttr: 'Copy to clipboard',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            titleAttr: 'Print Table',
                            className: 'btn-outline-primary btn-sm'
                        }
                    ],
                });
            $('.js-thead-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
            });

            $('.js-tbody-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
            });

        });

    </script>
     <script>
        var autoSave = $('#autoSave');
        var interval;
        var timer = function()
        {
            interval = setInterval(function()
            {
                //start slide...
                if (autoSave.prop('checked'))
                    saveToLocal();

                clearInterval(interval);
            }, 3000);
        };

        //save
        var saveToLocal = function()
        {
            localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
            console.log("saved");
        }

        //delete
        var removeFromLocal = function()
        {
            localStorage.removeItem("summernoteData");
            $('#saveToLocal').summernote('reset');
        }

        $(document).ready(function()
        {
            //init default
            $('.js-summernote').summernote(
            {
                height: 200,
                tabsize: 2,
                placeholder: "Type here...",
                dialogsFade: true,
                toolbar: [
                    ['style', ['style']],
                    ['font', ['strikethrough', 'superscript', 'subscript']],
                    ['font', ['bold', 'italic', 'underline', 'clear']],
                    ['fontsize', ['fontsize']],
                    ['fontname', ['fontname']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['height', ['height']]
                    ['table', ['table']],
                    ['insert', ['link', 'picture', 'video']],
                    ['view', ['fullscreen', 'codeview', 'help']]
                ],
                callbacks:
                {
                    //restore from localStorage
                    onInit: function(e)
                    {
                        $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
                    },
                    onChange: function(contents, $editable)
                    {
                        clearInterval(interval);
                        timer();
                    }
                }
            });

            //load emojis
            $.ajax(
            {
                url: 'https://api.github.com/emojis',
                async: false
            }).then(function(data)
            {
                window.emojis = Object.keys(data);
                window.emojiUrls = data;
            });

            //init emoji example
            $(".js-hint2emoji").summernote(
            {
                height: 100,
                toolbar: false,
                placeholder: 'type starting with : and any alphabet',
                hint:
                {
                    match: /:([\-+\w]+)$/,
                    search: function(keyword, callback)
                    {
                        callback($.grep(emojis, function(item)
                        {
                            return item.indexOf(keyword) === 0;
                        }));
                    },
                    template: function(item)
                    {
                        var content = emojiUrls[item];
                        return '<img src="' + content + '" width="20" /> :' + item + ':';
                    },
                    content: function(item)
                    {
                        var url = emojiUrls[item];
                        if (url)
                        {
                            return $('<img />').attr('src', url).css('width', 20)[0];
                        }
                        return '';
                    }
                }
            });

            //init mentions example
            $(".js-hint2mention").summernote(
            {
                height: 100,
                toolbar: false,
                placeholder: "type starting with @",
                hint:
                {
                    mentions: ['jayden', 'sam', 'alvin', 'david'],
                    match: /\B@(\w*)$/,
                    search: function(keyword, callback)
                    {
                        callback($.grep(this.mentions, function(item)
                        {
                            return item.indexOf(keyword) == 0;
                        }));
                    },
                    content: function(item)
                    {
                        return '@' + item;
                    }
                }
            });

        });

    </script>


   <!-- Search Selecet -->
 <script>
        $(document).ready(function()
        {
            $(function()
            {
                $('.select2').select2();

                $(".select2-placeholder-multiple").select2(
                {
                    placeholder: "Select State"
                });
                $(".js-hide-search").select2(
                {
                    minimumResultsForSearch: 1 / 0
                });
                $(".js-max-length").select2(
                {
                    maximumSelectionLength: 2,
                    placeholder: "Select maximum 2 items"
                });
                $(".select2-placeholder").select2(
                {
                    placeholder: "Select a state",
                    allowClear: true
                });

                $(".js-select2-icons").select2(
                {
                    minimumResultsForSearch: 1 / 0,
                    templateResult: icon,
                    templateSelection: icon,
                    escapeMarkup: function(elm)
                    {
                        return elm
                    }
                });

                function icon(elm)
                {
                    elm.element;
                    return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
                }


      $('#Bank').select2({
    placeholder: "select...",
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllSafes',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };

            	console.log(data);

        },
        data: function (params) {
          var query = {
            search: params.term
          };
          if (params.term == "*") query.items = [];
          return { json: JSON.stringify( query ) }
        }
    }
  });


$('#Bank').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});




  $('.Acc').select2({
    placeholder: "select...",
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllSubAccounts',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };

            	console.log(data);

        },
          data: function (params) {


                   var query = {
                            search: params.term,
                        };


              $.ajax({
                              url: 'AllSubAccountsJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('.Acc').empty();
                                  $.each(data, function(key, value){

                         $('.Acc').append('<option value="'+ key +'">' + value + '</option>');

                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });



        }
    }
  });


$('.Acc').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});




            });
        });



    </script>

<style>
@media  print {

  #js-page-content {
    display: none;
  }

      .close {
    display: none;
  }

}
</style>
<?php $__env->stopPush(); ?>


<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\erp\resources\views/admin/Accounts/InsurancePaper.blade.php ENDPATH**/ ?>
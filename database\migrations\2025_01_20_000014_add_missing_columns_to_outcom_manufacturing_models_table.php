<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToOutcomManufacturingModelsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('outcom_manufacturing_models', function (Blueprint $table) {
            // Add all the missing columns that are expected by the OutcomManufacturingModel model
            $table->string('Product_Code')->nullable();
            $table->string('P_Ar_Name')->nullable();
            $table->string('P_En_Name')->nullable();
            $table->decimal('Qty', 15, 4)->nullable();
            $table->decimal('SmallQty', 15, 4)->nullable();
            $table->string('SmallCode')->nullable();
            $table->string('Store')->nullable(); // Foreign key to stores
            $table->string('Product')->nullable(); // Foreign key to products
            $table->string('Unit')->nullable(); // Foreign key to measuerments
            $table->string('Model')->nullable(); // Foreign key to manufacturing_models
            $table->decimal('Cost', 15, 2)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('outcom_manufacturing_models', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn([
                'Product_Code', 'P_Ar_Name', 'P_En_Name', 'Qty', 'SmallQty', 'SmallCode', 
                'Store', 'Product', 'Unit', 'Model', 'Cost'
            ]);
        });
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPTypeColumnToProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('products', function (Blueprint $table) {
            // Add P_Type column if it doesn't exist
            if (!Schema::hasColumn('products', 'P_Type')) {
                $table->string('P_Type')->nullable()->after('id');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('products', function (Blueprint $table) {
            // Drop P_Type column if it exists
            if (Schema::hasColumn('products', 'P_Type')) {
                $table->dropColumn('P_Type');
            }
        });
    }
}

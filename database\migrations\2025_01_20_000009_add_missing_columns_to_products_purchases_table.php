<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToProductsPurchasesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('products_purchases', function (Blueprint $table) {
            // Add all the missing columns that are expected by the ProductsPurchases model
            $table->string('Product_Code')->nullable();
            $table->string('P_Ar_Name')->nullable();
            $table->string('P_En_Name')->nullable();
            $table->string('V_Name')->nullable();
            $table->string('VV_Name')->nullable();
            $table->decimal('Original_Qty', 15, 4)->nullable();
            $table->string('SmallCode')->nullable();
            $table->decimal('Qty', 15, 4)->nullable();
            $table->decimal('SmallQty', 15, 4)->nullable();
            $table->decimal('Price', 15, 2)->nullable();
            $table->decimal('Discount', 15, 2)->nullable();
            $table->decimal('TDiscount', 15, 2)->nullable();
            $table->decimal('Tax', 15, 2)->nullable();
            $table->decimal('Total_Bf_Tax', 15, 2)->nullable();
            $table->decimal('Total_Tax', 15, 2)->nullable();
            $table->decimal('Total', 15, 2)->nullable();
            $table->string('Store')->nullable(); // Foreign key to stores
            $table->string('Product')->nullable(); // Foreign key to products
            $table->date('Exp_Date')->nullable();
            $table->string('V1')->nullable(); // Foreign key to sub_virables
            $table->string('V2')->nullable(); // Foreign key to sub_virables
            $table->string('Unit')->nullable(); // Foreign key to measuerments
            $table->string('Purchase')->nullable(); // Foreign key to purchases
            $table->date('Date')->nullable();
            $table->string('Code')->nullable();
            $table->string('Refernce_Number')->nullable();
            $table->string('Safe')->nullable(); // Foreign key to acccounting_manuals
            $table->string('Vendor')->nullable(); // Foreign key to acccounting_manuals
            $table->string('Delegate')->nullable(); // Foreign key to employess
            $table->string('Coin')->nullable(); // Foreign key to coins
            $table->string('User')->nullable(); // Foreign key to admins
            $table->string('Cost_Center')->nullable(); // Foreign key to cost_centers
            $table->string('Type')->nullable();
            $table->string('Ship')->nullable();
            $table->string('Payment_Method')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('products_purchases', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn([
                'Product_Code', 'P_Ar_Name', 'P_En_Name', 'V_Name', 'VV_Name', 'Original_Qty', 'SmallCode',
                'Qty', 'SmallQty', 'Price', 'Discount', 'TDiscount', 'Tax', 'Total_Bf_Tax', 'Total_Tax',
                'Total', 'Store', 'Product', 'Exp_Date', 'V1', 'V2', 'Unit', 'Purchase', 'Date', 'Code',
                'Refernce_Number', 'Safe', 'Vendor', 'Delegate', 'Coin', 'User', 'Cost_Center', 'Type',
                'Ship', 'Payment_Method'
            ]);
        });
    }
}
